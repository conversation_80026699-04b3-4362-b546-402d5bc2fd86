package ollama

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

func TestNewClient(t *testing.T) {
	baseURL := "http://test.example.com"
	timeout := 30 * time.Second
	verbose := true
	
	client := NewClient(baseURL, timeout, verbose)
	
	if client.baseURL != baseURL {
		t.<PERSON>rf("期望baseURL为 '%s', 实际为 '%s'", baseURL, client.baseURL)
	}
	
	if client.httpClient.Timeout != timeout {
		t.Errorf("期望超时为 %v, 实际为 %v", timeout, client.httpClient.Timeout)
	}
	
	if client.verbose != verbose {
		t.<PERSON><PERSON>rf("期望verbose为 %v, 实际为 %v", verbose, client.verbose)
	}
}

func TestListModels(t *testing.T) {
	// 创建模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/api/tags" {
			t.<PERSON><PERSON>("期望路径为 '/api/tags', 实际为 '%s'", r.URL.Path)
		}
		
		if r.Method != "GET" {
			t.<PERSON><PERSON><PERSON>("期望方法为 'GET', 实际为 '%s'", r.Method)
		}
		
		response := ListModelsResponse{
			Models: []Model{
				{
					Name:   "test-model",
					Size:   1024,
					Digest: "sha256:test",
				},
			},
		}
		
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()
	
	client := NewClient(server.URL, 30*time.Second, false)
	ctx := context.Background()
	
	resp, err := client.ListModels(ctx)
	if err != nil {
		t.Fatalf("ListModels失败: %v", err)
	}
	
	if len(resp.Models) != 1 {
		t.Errorf("期望1个模型, 实际为 %d", len(resp.Models))
	}
	
	if resp.Models[0].Name != "test-model" {
		t.Errorf("期望模型名称为 'test-model', 实际为 '%s'", resp.Models[0].Name)
	}
}

func TestGenerate(t *testing.T) {
	// 创建模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/api/generate" {
			t.Errorf("期望路径为 '/api/generate', 实际为 '%s'", r.URL.Path)
		}
		
		if r.Method != "POST" {
			t.Errorf("期望方法为 'POST', 实际为 '%s'", r.Method)
		}
		
		var req GenerateRequest
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			t.Fatalf("解析请求失败: %v", err)
		}
		
		if req.Model != "test-model" {
			t.Errorf("期望模型为 'test-model', 实际为 '%s'", req.Model)
		}
		
		if req.Prompt != "test prompt" {
			t.Errorf("期望提示词为 'test prompt', 实际为 '%s'", req.Prompt)
		}
		
		response := GenerateResponse{
			Model:    req.Model,
			Response: "test response",
			Done:     true,
		}
		
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()
	
	client := NewClient(server.URL, 30*time.Second, false)
	ctx := context.Background()
	
	resp, err := client.Generate(ctx, "test-model", "test prompt", false)
	if err != nil {
		t.Fatalf("Generate失败: %v", err)
	}
	
	if resp.Model != "test-model" {
		t.Errorf("期望模型为 'test-model', 实际为 '%s'", resp.Model)
	}
	
	if resp.Response != "test response" {
		t.Errorf("期望响应为 'test response', 实际为 '%s'", resp.Response)
	}
	
	if !resp.Done {
		t.Errorf("期望Done为true, 实际为 %v", resp.Done)
	}
}

func TestChat(t *testing.T) {
	// 创建模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/api/chat" {
			t.Errorf("期望路径为 '/api/chat', 实际为 '%s'", r.URL.Path)
		}
		
		if r.Method != "POST" {
			t.Errorf("期望方法为 'POST', 实际为 '%s'", r.Method)
		}
		
		var req ChatRequest
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			t.Fatalf("解析请求失败: %v", err)
		}
		
		if req.Model != "test-model" {
			t.Errorf("期望模型为 'test-model', 实际为 '%s'", req.Model)
		}
		
		if len(req.Messages) != 1 {
			t.Errorf("期望1条消息, 实际为 %d", len(req.Messages))
		}
		
		if req.Messages[0].Role != "user" {
			t.Errorf("期望角色为 'user', 实际为 '%s'", req.Messages[0].Role)
		}
		
		response := ChatResponse{
			Model: req.Model,
			Message: ChatMessage{
				Role:    "assistant",
				Content: "test response",
			},
			Done: true,
		}
		
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()
	
	client := NewClient(server.URL, 30*time.Second, false)
	ctx := context.Background()
	
	messages := []ChatMessage{
		{
			Role:    "user",
			Content: "test message",
		},
	}
	
	resp, err := client.Chat(ctx, "test-model", messages, false)
	if err != nil {
		t.Fatalf("Chat失败: %v", err)
	}
	
	if resp.Model != "test-model" {
		t.Errorf("期望模型为 'test-model', 实际为 '%s'", resp.Model)
	}
	
	if resp.Message.Role != "assistant" {
		t.Errorf("期望角色为 'assistant', 实际为 '%s'", resp.Message.Role)
	}
	
	if resp.Message.Content != "test response" {
		t.Errorf("期望内容为 'test response', 实际为 '%s'", resp.Message.Content)
	}
}

func TestErrorHandling(t *testing.T) {
	// 创建返回错误的模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
		errorResp := ErrorResponse{
			Error: "test error message",
		}
		json.NewEncoder(w).Encode(errorResp)
	}))
	defer server.Close()
	
	client := NewClient(server.URL, 30*time.Second, false)
	ctx := context.Background()
	
	_, err := client.ListModels(ctx)
	if err == nil {
		t.Fatalf("期望返回错误, 但没有错误")
	}
	
	expectedError := "HTTP 500: test error message"
	if err.Error() != expectedError {
		t.Errorf("期望错误消息为 '%s', 实际为 '%s'", expectedError, err.Error())
	}
}
