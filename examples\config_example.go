// +build ignore

package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	"ollama-cli/internal/config"
)

// 这个示例展示了如何使用配置管理功能
func main() {
	// 示例1: 创建默认配置
	fmt.Println("=== 默认配置示例 ===")
	defaultCfg := config.DefaultConfig()
	fmt.Printf("默认主机: %s\n", defaultCfg.Host)
	fmt.Printf("默认超时: %d秒\n", defaultCfg.Timeout)
	fmt.Printf("默认详细模式: %v\n", defaultCfg.Verbose)

	// 示例2: 保存配置到文件
	fmt.Println("\n=== 保存配置示例 ===")
	customCfg := &config.Config{
		Host:    "http://remote-server:11434",
		Verbose: true,
		Timeout: 60,
	}

	// 获取临时目录
	tempDir := os.TempDir()
	configPath := filepath.Join(tempDir, "ollama-cli-example.json")

	err := customCfg.Save(configPath)
	if err != nil {
		log.Fatalf("保存配置失败: %v", err)
	}
	fmt.Printf("配置已保存到: %s\n", configPath)

	// 示例3: 从文件加载配置
	fmt.Println("\n=== 加载配置示例 ===")
	loadedCfg, err := config.Load(configPath, "http://localhost:11434", false)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	fmt.Printf("加载的主机: %s\n", loadedCfg.Host)
	fmt.Printf("加载的超时: %d秒\n", loadedCfg.Timeout)
	fmt.Printf("加载的详细模式: %v\n", loadedCfg.Verbose)

	// 示例4: 命令行参数覆盖配置文件
	fmt.Println("\n=== 参数覆盖示例 ===")
	overrideCfg, err := config.Load(configPath, "http://override.example.com:8080", true)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	fmt.Printf("覆盖后的主机: %s\n", overrideCfg.Host)
	fmt.Printf("覆盖后的详细模式: %v\n", overrideCfg.Verbose)
	fmt.Printf("保持的超时设置: %d秒\n", overrideCfg.Timeout)

	// 清理临时文件
	os.Remove(configPath)
	fmt.Printf("\n临时配置文件已删除: %s\n", configPath)
}
