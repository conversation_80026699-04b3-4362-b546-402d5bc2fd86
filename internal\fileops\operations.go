package fileops

import (
	"bufio"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// BatchJob 批处理任务
type BatchJob struct {
	ID          string    `json:"id"`
	Model       string    `json:"model"`
	Prompts     []string  `json:"prompts"`
	Results     []string  `json:"results"`
	Status      string    `json:"status"` // pending, running, completed, failed
	CreatedAt   time.Time `json:"created_at"`
	CompletedAt time.Time `json:"completed_at,omitempty"`
	ErrorCount  int       `json:"error_count"`
}

// FileManager 文件操作管理器
type FileManager struct {
	workDir string
}

// NewFileManager 创建文件管理器
func NewFileManager(workDir string) *FileManager {
	return &FileManager{
		workDir: workDir,
	}
}

// ReadPromptsFromFile 从文件读取提示词
func (fm *FileManager) ReadPromptsFromFile(filename string) ([]string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	var prompts []string
	scanner := bufio.NewScanner(file)
	
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" && !strings.HasPrefix(line, "#") { // 跳过空行和注释
			prompts = append(prompts, line)
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("读取文件失败: %w", err)
	}

	return prompts, nil
}

// WriteResultsToFile 将结果写入文件
func (fm *FileManager) WriteResultsToFile(filename string, results []string) error {
	if err := fm.ensureWorkDir(); err != nil {
		return err
	}

	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("创建文件失败: %w", err)
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	defer writer.Flush()

	for i, result := range results {
		if i > 0 {
			writer.WriteString("\n" + strings.Repeat("=", 50) + "\n")
		}
		writer.WriteString(fmt.Sprintf("结果 %d:\n", i+1))
		writer.WriteString(result)
		writer.WriteString("\n")
	}

	return nil
}

// WriteResultsToJSON 将结果写入JSON文件
func (fm *FileManager) WriteResultsToJSON(filename string, job *BatchJob) error {
	if err := fm.ensureWorkDir(); err != nil {
		return err
	}

	data, err := json.MarshalIndent(job, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化JSON失败: %w", err)
	}

	return os.WriteFile(filename, data, 0644)
}

// AppendToFile 追加内容到文件
func (fm *FileManager) AppendToFile(filename, content string) error {
	if err := fm.ensureWorkDir(); err != nil {
		return err
	}

	file, err := os.OpenFile(filename, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	_, err = file.WriteString(content)
	return err
}

// CreateBatchJob 创建批处理任务
func (fm *FileManager) CreateBatchJob(model string, prompts []string) *BatchJob {
	return &BatchJob{
		ID:        fmt.Sprintf("batch_%d", time.Now().UnixNano()),
		Model:     model,
		Prompts:   prompts,
		Results:   make([]string, 0, len(prompts)),
		Status:    "pending",
		CreatedAt: time.Now(),
	}
}

// SaveBatchJob 保存批处理任务
func (fm *FileManager) SaveBatchJob(job *BatchJob) error {
	if err := fm.ensureWorkDir(); err != nil {
		return err
	}

	filename := filepath.Join(fm.workDir, "batch_jobs", job.ID+".json")
	
	// 确保批处理任务目录存在
	if err := os.MkdirAll(filepath.Dir(filename), 0755); err != nil {
		return err
	}

	return fm.WriteResultsToJSON(filename, job)
}

// LoadBatchJob 加载批处理任务
func (fm *FileManager) LoadBatchJob(jobID string) (*BatchJob, error) {
	filename := filepath.Join(fm.workDir, "batch_jobs", jobID+".json")
	
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取任务文件失败: %w", err)
	}

	var job BatchJob
	if err := json.Unmarshal(data, &job); err != nil {
		return nil, fmt.Errorf("解析任务数据失败: %w", err)
	}

	return &job, nil
}

// ListBatchJobs 列出所有批处理任务
func (fm *FileManager) ListBatchJobs() ([]*BatchJob, error) {
	jobsDir := filepath.Join(fm.workDir, "batch_jobs")
	
	if _, err := os.Stat(jobsDir); os.IsNotExist(err) {
		return []*BatchJob{}, nil
	}

	files, err := os.ReadDir(jobsDir)
	if err != nil {
		return nil, fmt.Errorf("读取任务目录失败: %w", err)
	}

	var jobs []*BatchJob
	for _, file := range files {
		if !file.IsDir() && filepath.Ext(file.Name()) == ".json" {
			jobID := file.Name()[:len(file.Name())-5]
			job, err := fm.LoadBatchJob(jobID)
			if err != nil {
				continue // 跳过损坏的任务文件
			}
			jobs = append(jobs, job)
		}
	}

	return jobs, nil
}

// ExportToCSV 导出结果到CSV文件
func (fm *FileManager) ExportToCSV(filename string, prompts, results []string) error {
	if err := fm.ensureWorkDir(); err != nil {
		return err
	}

	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("创建CSV文件失败: %w", err)
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	defer writer.Flush()

	// 写入CSV头部
	writer.WriteString("序号,提示词,生成结果\n")

	// 写入数据
	for i := 0; i < len(prompts) && i < len(results); i++ {
		prompt := strings.ReplaceAll(prompts[i], "\"", "\"\"")
		result := strings.ReplaceAll(results[i], "\"", "\"\"")
		writer.WriteString(fmt.Sprintf("%d,\"%s\",\"%s\"\n", i+1, prompt, result))
	}

	return nil
}

// CreatePromptTemplate 创建提示词模板文件
func (fm *FileManager) CreatePromptTemplate(filename string) error {
	if err := fm.ensureWorkDir(); err != nil {
		return err
	}

	template := `# 提示词模板文件
# 每行一个提示词，以#开头的行为注释

# 示例提示词：
写一首关于春天的诗
解释什么是人工智能
翻译以下文本为英文：你好，世界
总结以下内容的要点：[在这里插入要总结的内容]

# 你可以在这里添加更多提示词
`

	return os.WriteFile(filename, []byte(template), 0644)
}

// ValidateFile 验证文件格式
func (fm *FileManager) ValidateFile(filename string) error {
	file, err := os.Open(filename)
	if err != nil {
		return fmt.Errorf("无法打开文件: %w", err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	lineCount := 0
	promptCount := 0

	for scanner.Scan() {
		lineCount++
		line := strings.TrimSpace(scanner.Text())
		
		if line != "" && !strings.HasPrefix(line, "#") {
			promptCount++
		}
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("读取文件时出错: %w", err)
	}

	if promptCount == 0 {
		return fmt.Errorf("文件中没有找到有效的提示词")
	}

	return nil
}

// GetFileInfo 获取文件信息
func (fm *FileManager) GetFileInfo(filename string) (map[string]interface{}, error) {
	stat, err := os.Stat(filename)
	if err != nil {
		return nil, fmt.Errorf("获取文件信息失败: %w", err)
	}

	prompts, err := fm.ReadPromptsFromFile(filename)
	if err != nil {
		return nil, err
	}

	info := map[string]interface{}{
		"filename":     filename,
		"size":         stat.Size(),
		"modified":     stat.ModTime(),
		"prompt_count": len(prompts),
	}

	return info, nil
}

// CleanupOldJobs 清理旧的批处理任务
func (fm *FileManager) CleanupOldJobs(olderThan time.Duration) error {
	jobs, err := fm.ListBatchJobs()
	if err != nil {
		return err
	}

	cutoff := time.Now().Add(-olderThan)
	cleaned := 0

	for _, job := range jobs {
		if job.CreatedAt.Before(cutoff) {
			filename := filepath.Join(fm.workDir, "batch_jobs", job.ID+".json")
			if err := os.Remove(filename); err == nil {
				cleaned++
			}
		}
	}

	fmt.Printf("清理了 %d 个旧任务\n", cleaned)
	return nil
}

// ensureWorkDir 确保工作目录存在
func (fm *FileManager) ensureWorkDir() error {
	return os.MkdirAll(fm.workDir, 0755)
}

// BackupFile 备份文件
func (fm *FileManager) BackupFile(filename string) (string, error) {
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return "", fmt.Errorf("文件不存在: %s", filename)
	}

	timestamp := time.Now().Format("20060102_150405")
	backupName := fmt.Sprintf("%s.backup_%s", filename, timestamp)

	input, err := os.ReadFile(filename)
	if err != nil {
		return "", fmt.Errorf("读取原文件失败: %w", err)
	}

	err = os.WriteFile(backupName, input, 0644)
	if err != nil {
		return "", fmt.Errorf("创建备份文件失败: %w", err)
	}

	return backupName, nil
}

// CreateOutputDirectory 创建输出目录
func (fm *FileManager) CreateOutputDirectory(name string) (string, error) {
	timestamp := time.Now().Format("20060102_150405")
	dirName := fmt.Sprintf("%s_%s", name, timestamp)
	dirPath := filepath.Join(fm.workDir, "outputs", dirName)

	err := os.MkdirAll(dirPath, 0755)
	if err != nil {
		return "", fmt.Errorf("创建输出目录失败: %w", err)
	}

	return dirPath, nil
}
