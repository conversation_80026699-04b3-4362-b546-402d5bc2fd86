package ollama

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// Client Ollama API客户端
type Client struct {
	baseURL    string
	httpClient *http.Client
	verbose    bool
}

// NewClient 创建新的Ollama客户端
func NewClient(baseURL string, timeout time.Duration, verbose bool) *Client {
	return &Client{
		baseURL: strings.TrimSuffix(baseURL, "/"),
		httpClient: &http.Client{
			Timeout: timeout,
		},
		verbose: verbose,
	}
}

// ListModels 列出所有可用模型
func (c *Client) ListModels(ctx context.Context) (*ListModelsResponse, error) {
	url := c.baseURL + "/api/tags"
	
	if c.verbose {
		fmt.Printf("请求URL: %s\n", url)
	}

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, c.handleErrorResponse(resp)
	}

	var result ListModelsResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &result, nil
}

// Generate 生成文本
func (c *Client) Generate(ctx context.Context, model, prompt string, stream bool) (*GenerateResponse, error) {
	url := c.baseURL + "/api/generate"

	req := GenerateRequest{
		Model:  model,
		Prompt: prompt,
		Stream: stream,
	}

	if stream {
		return c.doGenerateStreamRequest(ctx, url, req)
	}
	return c.doGenerateRequest(ctx, url, req)
}

// GenerateStream 流式生成文本
func (c *Client) GenerateStream(ctx context.Context, model, prompt string, callback func(GenerateResponse) error) error {
	url := c.baseURL + "/api/generate"

	req := GenerateRequest{
		Model:  model,
		Prompt: prompt,
		Stream: true,
	}

	return c.doGenerateStreamWithCallback(ctx, url, req, callback)
}

// Chat 进行对话
func (c *Client) Chat(ctx context.Context, model string, messages []ChatMessage, stream bool) (*ChatResponse, error) {
	url := c.baseURL + "/api/chat"

	req := ChatRequest{
		Model:    model,
		Messages: messages,
		Stream:   stream,
	}

	if stream {
		return c.doChatStreamRequest(ctx, url, req)
	}
	return c.doChatRequest(ctx, url, req)
}

// ChatStream 流式聊天
func (c *Client) ChatStream(ctx context.Context, model string, messages []ChatMessage, callback func(ChatResponse) error) error {
	url := c.baseURL + "/api/chat"

	req := ChatRequest{
		Model:    model,
		Messages: messages,
		Stream:   true,
	}

	return c.doChatStreamWithCallback(ctx, url, req, callback)
}

// PullModel 下载模型
func (c *Client) PullModel(ctx context.Context, model string, stream bool) error {
	url := c.baseURL + "/api/pull"
	
	req := PullRequest{
		Name:   model,
		Stream: stream,
	}

	jsonData, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("序列化请求失败: %w", err)
	}

	if c.verbose {
		fmt.Printf("请求URL: %s\n", url)
		fmt.Printf("请求数据: %s\n", string(jsonData))
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return c.handleErrorResponse(resp)
	}

	if stream {
		return c.handlePullStream(resp.Body)
	}

	var result PullResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	fmt.Printf("模型下载完成: %s\n", result.Status)
	return nil
}

// ShowModel 显示模型信息
func (c *Client) ShowModel(ctx context.Context, model string) (*ShowResponse, error) {
	url := c.baseURL + "/api/show"
	
	req := ShowRequest{
		Name: model,
	}

	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	if c.verbose {
		fmt.Printf("请求URL: %s\n", url)
		fmt.Printf("请求数据: %s\n", string(jsonData))
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, c.handleErrorResponse(resp)
	}

	var result ShowResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &result, nil
}

// DeleteModel 删除模型
func (c *Client) DeleteModel(ctx context.Context, model string) error {
	url := c.baseURL + "/api/delete"
	
	req := DeleteRequest{
		Name: model,
	}

	jsonData, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("序列化请求失败: %w", err)
	}

	if c.verbose {
		fmt.Printf("请求URL: %s\n", url)
		fmt.Printf("请求数据: %s\n", string(jsonData))
	}

	httpReq, err := http.NewRequestWithContext(ctx, "DELETE", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return c.handleErrorResponse(resp)
	}

	fmt.Printf("模型删除成功: %s\n", model)
	return nil
}

// doGenerateRequest 执行生成请求
func (c *Client) doGenerateRequest(ctx context.Context, url string, req GenerateRequest) (*GenerateResponse, error) {
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	if c.verbose {
		fmt.Printf("请求URL: %s\n", url)
		fmt.Printf("请求数据: %s\n", string(jsonData))
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, c.handleErrorResponse(resp)
	}

	var result GenerateResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &result, nil
}

// handleErrorResponse 处理错误响应
func (c *Client) handleErrorResponse(resp *http.Response) error {
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("HTTP %d: 无法读取错误响应", resp.StatusCode)
	}

	var errorResp ErrorResponse
	if err := json.Unmarshal(body, &errorResp); err != nil {
		return fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(body))
	}

	return fmt.Errorf("HTTP %d: %s", resp.StatusCode, errorResp.Error)
}

// doGenerateStreamRequest 执行流式生成请求
func (c *Client) doGenerateStreamRequest(ctx context.Context, url string, req GenerateRequest) (*GenerateResponse, error) {
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	if c.verbose {
		fmt.Printf("请求URL: %s\n", url)
		fmt.Printf("请求数据: %s\n", string(jsonData))
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, c.handleErrorResponse(resp)
	}

	var finalResult GenerateResponse
	scanner := bufio.NewScanner(resp.Body)

	for scanner.Scan() {
		var result GenerateResponse
		if err := json.Unmarshal(scanner.Bytes(), &result); err != nil {
			continue
		}

		// 实时输出生成的内容
		if result.Response != "" {
			fmt.Print(result.Response)
		}

		finalResult = result
		if result.Done {
			break
		}
	}

	fmt.Println() // 换行
	return &finalResult, scanner.Err()
}

// doGenerateStreamWithCallback 执行带回调的流式生成请求
func (c *Client) doGenerateStreamWithCallback(ctx context.Context, url string, req GenerateRequest, callback func(GenerateResponse) error) error {
	jsonData, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("序列化请求失败: %w", err)
	}

	if c.verbose {
		fmt.Printf("请求URL: %s\n", url)
		fmt.Printf("请求数据: %s\n", string(jsonData))
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return c.handleErrorResponse(resp)
	}

	scanner := bufio.NewScanner(resp.Body)
	for scanner.Scan() {
		var result GenerateResponse
		if err := json.Unmarshal(scanner.Bytes(), &result); err != nil {
			continue
		}

		if err := callback(result); err != nil {
			return err
		}

		if result.Done {
			break
		}
	}

	return scanner.Err()
}

// doChatRequest 执行聊天请求
func (c *Client) doChatRequest(ctx context.Context, url string, req ChatRequest) (*ChatResponse, error) {
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	if c.verbose {
		fmt.Printf("请求URL: %s\n", url)
		fmt.Printf("请求数据: %s\n", string(jsonData))
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, c.handleErrorResponse(resp)
	}

	var result ChatResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &result, nil
}

// doChatStreamRequest 执行流式聊天请求
func (c *Client) doChatStreamRequest(ctx context.Context, url string, req ChatRequest) (*ChatResponse, error) {
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	if c.verbose {
		fmt.Printf("请求URL: %s\n", url)
		fmt.Printf("请求数据: %s\n", string(jsonData))
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, c.handleErrorResponse(resp)
	}

	var finalResult ChatResponse
	scanner := bufio.NewScanner(resp.Body)

	for scanner.Scan() {
		var result ChatResponse
		if err := json.Unmarshal(scanner.Bytes(), &result); err != nil {
			continue
		}

		// 实时输出生成的内容
		if result.Message.Content != "" {
			fmt.Print(result.Message.Content)
		}

		finalResult = result
		if result.Done {
			break
		}
	}

	fmt.Println() // 换行
	return &finalResult, scanner.Err()
}

// doChatStreamWithCallback 执行带回调的流式聊天请求
func (c *Client) doChatStreamWithCallback(ctx context.Context, url string, req ChatRequest, callback func(ChatResponse) error) error {
	jsonData, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("序列化请求失败: %w", err)
	}

	if c.verbose {
		fmt.Printf("请求URL: %s\n", url)
		fmt.Printf("请求数据: %s\n", string(jsonData))
	}

	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return c.handleErrorResponse(resp)
	}

	scanner := bufio.NewScanner(resp.Body)
	for scanner.Scan() {
		var result ChatResponse
		if err := json.Unmarshal(scanner.Bytes(), &result); err != nil {
			continue
		}

		if err := callback(result); err != nil {
			return err
		}

		if result.Done {
			break
		}
	}

	return scanner.Err()
}

// handlePullStream 处理拉取模型的流式响应
func (c *Client) handlePullStream(body io.Reader) error {
	scanner := bufio.NewScanner(body)
	for scanner.Scan() {
		var resp PullResponse
		if err := json.Unmarshal(scanner.Bytes(), &resp); err != nil {
			continue
		}

		if resp.Total > 0 {
			progress := float64(resp.Completed) / float64(resp.Total) * 100
			fmt.Printf("\r下载进度: %.1f%% (%d/%d bytes)", progress, resp.Completed, resp.Total)
		} else {
			fmt.Printf("\r%s", resp.Status)
		}
	}
	fmt.Println() // 换行
	return scanner.Err()
}
