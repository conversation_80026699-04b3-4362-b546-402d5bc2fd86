# 🚀 Ollama CLI 快速开始指南

## 5分钟上手指南

### 第1步：准备环境

```bash
# 1. 确保Ollama服务运行
ollama serve

# 2. 下载一个模型
ollama pull llama2

# 3. 验证模型可用
ollama list
```

### 第2步：构建工具

```bash
# 克隆或下载项目到本地
cd ollama-cli

# 构建可执行文件
go build -o ollama-cli.exe main.go  # Windows
# 或
go build -o ollama-cli main.go      # Linux/macOS
```

### 第3步：基础使用

```bash
# 查看帮助
./ollama-cli

# 列出模型
./ollama-cli list

# 简单对话
./ollama-cli chat llama2
```

### 第4步：体验核心功能

#### 🗂️ 会话管理
```bash
# 创建会话
./ollama-cli sessions create "我的第一个会话" llama2

# 使用会话对话
./ollama-cli chat-session session_xxx
```

#### 🎨 模板系统
```bash
# 查看内置模板
./ollama-cli template list

# 使用翻译模板
./ollama-cli template use builtin_translate
```

#### 📊 批处理
```bash
# 创建提示词文件
./ollama-cli file template my-prompts.txt

# 编辑文件，添加几个提示词，然后：
./ollama-cli batch run llama2 my-prompts.txt
```

### 第5步：探索高级功能

```bash
# 流式生成
./ollama-cli stream-generate llama2 "写一个故事"

# 性能监控
./ollama-cli -verbose generate llama2 "测试"

# 会话统计
./ollama-cli sessions stats session_xxx
```

## 🎯 常用场景

### 内容创作
```bash
./ollama-cli sessions create "小说创作" llama2
./ollama-cli template use builtin_creative_writing
./ollama-cli chat-session session_xxx
```

### 代码审查
```bash
./ollama-cli template use builtin_code_review
# 按提示输入代码和语言
```

### 批量翻译
```bash
echo "你好世界
谢谢
再见" > texts.txt
./ollama-cli batch run llama2 texts.txt
```

## 🆘 遇到问题？

```bash
# 检查连接
./ollama-cli -verbose list

# 查看详细错误
./ollama-cli -verbose [command] 2>&1 | tee debug.log
```

**恭喜！你已经掌握了Ollama CLI的基础使用。查看完整的README.md了解更多高级功能。**
