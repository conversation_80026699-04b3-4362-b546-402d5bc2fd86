# 🚀 Ollama CLI - AI工作流工具

<div align="center">

![Go Version](https://img.shields.io/badge/Go-1.21+-blue.svg)
![License](https://img.shields.io/badge/License-MIT-green.svg)
![Platform](https://img.shields.io/badge/Platform-Windows%20%7C%20Linux%20%7C%20macOS-lightgrey.svg)

**一个功能丰富的Ollama API增强客户端**

[功能特性](#-功能特性) • [快速开始](#-快速开始) • [使用指南](#-使用指南) • [高级功能](#-高级功能) • [贡献](#-贡献)

</div>

---

## 📖 项目简介

Ollama CLI是一个AI工作流工具，基于官方Ollama构建，提供了丰富的增强功能。它不仅保留了Ollama的所有基础功能，还添加了会话管理、批处理、模板系统、性能监控等企业级特性，让AI模型的使用更加高效和专业。

### 🎯 设计理念

- **工作流优先** - 为内容创作、代码审查、翻译等专业场景设计
- **用户体验至上** - 中文界面、智能提示、流式显示
- **数据安全可控** - 本地运行、数据自主、隐私保护
- **高效批量处理** - 支持大规模文本处理任务
- **可扩展架构** - 模块化设计，易于定制和扩展

### 🆚 与官方Ollama的区别

| 功能 | 官方Ollama | Ollama CLI |
|------|------------|------------|
| 基础模型操作 | ✅ | ✅ 增强版 |
| 会话管理 | ❌ | ✅ 完整支持 |
| 批处理 | ❌ | ✅ 多格式支持 |
| 模板系统 | ❌ | ✅ 内置+自定义 |
| 性能监控 | 基础 | ✅ 详细统计 |
| 用户界面 | 英文 | ✅ 中文界面 |
| 文件操作 | ❌ | ✅ 完整支持 |

## ✨ 功能特性

### 🎯 核心功能
- **🤖 智能对话** - 支持流式对话，实时显示生成内容
- **📊 批量处理** - 一次处理数百个提示词，支持多种输出格式
- **🗂️ 会话管理** - 保存、恢复、管理对话历史
- **🎨 模板系统** - 内置专业模板，支持自定义模板
- **⚡ 性能监控** - 实时统计生成速度、token使用量
- **📁 文件操作** - 文件验证、备份、批处理管理

### 🛠️ 技术特性
- **零外部依赖** - 仅使用Go标准库，部署简单
- **跨平台支持** - Windows、Linux、macOS全平台支持
- **模块化架构** - 清晰的代码结构，易于维护和扩展
- **完善测试** - 全面的单元测试覆盖
- **智能错误处理** - 用户友好的错误提示和恢复机制

## 🚀 快速开始

### 📋 系统要求

- **Go 1.21+** (仅构建时需要)
- **Ollama服务** (运行时需要)
- **操作系统**: Windows 10+, Linux, macOS

### 📦 安装方式

#### 方式一：从源码构建
```bash
# 克隆项目
git clone https://github.com/your-username/ollama-cli.git
cd ollama-cli

# 构建可执行文件
go build -o ollama-cli main.go

# Windows用户
go build -o ollama-cli.exe main.go
```

#### 方式二：使用Makefile
```bash
# 构建单平台版本
make build

# 构建多平台版本
make build-all

# 运行测试
make test

# 查看所有可用命令
make help
```

### ⚡ 快速验证

```bash
# 查看版本信息
./ollama-cli -version

# 查看帮助信息
./ollama-cli

# 测试连接（需要Ollama服务运行）
./ollama-cli list
```

### 🔧 Ollama服务设置

在使用本工具前，请确保Ollama服务正在运行：

```bash
# 安装Ollama（如果还没有）
# 访问 https://ollama.ai 下载安装

# 启动Ollama服务
ollama serve

# 下载一个模型进行测试
ollama pull llama2

# 验证模型可用
ollama list
```

## 📚 使用指南

### 🎮 基础命令

#### 模型管理
```bash
# 列出所有可用模型
./ollama-cli list

# 下载新模型
./ollama-cli pull llama2
./ollama-cli pull codellama

# 查看模型详细信息
./ollama-cli show llama2

# 删除不需要的模型
./ollama-cli delete old-model
```

#### 文本生成
```bash
# 基础文本生成
./ollama-cli generate llama2 "写一首关于春天的诗"

# 流式生成（推荐，实时显示）
./ollama-cli stream-generate llama2 "讲一个有趣的故事"

# 启用详细输出查看性能统计
./ollama-cli -verbose generate llama2 "解释人工智能"
```

#### 交互式对话
```bash
# 开始对话
./ollama-cli chat llama2

# 对话中可用命令：
# - 输入 'exit' 退出对话
# - 输入 'stream' 切换流式/普通模式
# - 正常输入进行对话
```

### 🗂️ 会话管理

会话管理是本工具的核心优势之一，让你可以保存和恢复对话历史。

```bash
# 创建新会话
./ollama-cli sessions create "工作讨论" llama2
./ollama-cli sessions create "小说创作" llama2

# 列出所有会话
./ollama-cli sessions list

# 查看会话详情
./ollama-cli sessions show session_1234567890

# 使用已有会话继续对话
./ollama-cli chat-session session_1234567890

# 查看会话统计信息
./ollama-cli sessions stats session_1234567890

# 导出会话数据
./ollama-cli sessions export session_1234567890 my-chat.json

# 导入会话数据
./ollama-cli sessions import my-chat.json

# 删除会话
./ollama-cli sessions delete session_1234567890
```

### 📊 批处理功能

批处理功能让你可以一次处理多个提示词，大大提高工作效率。

#### 准备输入文件
```bash
# 创建提示词模板文件
./ollama-cli file template my-prompts.txt

# 编辑文件，添加你的提示词（每行一个）
# 示例内容：
# 写一首关于秋天的诗
# 解释什么是机器学习
# 翻译以下文本为英文：你好，世界
```

#### 运行批处理
```bash
# 运行批处理任务
./ollama-cli batch run llama2 my-prompts.txt

# 列出所有批处理任务
./ollama-cli batch list

# 查看任务详情
./ollama-cli batch show batch_1234567890

# 导出结果为不同格式
./ollama-cli batch export batch_1234567890 results.txt
./ollama-cli batch export batch_1234567890 results.json
./ollama-cli batch export batch_1234567890 results.csv
```

### 🎨 模板系统

模板系统提供了预定义的专业提示词模板，让你的工作更加标准化。

#### 使用内置模板
```bash
# 列出所有可用模板
./ollama-cli template list

# 查看模板详情
./ollama-cli template show builtin_translate

# 使用翻译模板
./ollama-cli template use builtin_translate
# 按提示输入：文本内容、目标语言等

# 使用代码审查模板
./ollama-cli template use builtin_code_review

# 使用创意写作模板
./ollama-cli template use builtin_creative_writing

# 搜索相关模板
./ollama-cli template search "翻译"
```

#### 自定义模板
```bash
# 交互式创建新模板
./ollama-cli template create

# 导出模板
./ollama-cli template export tmpl_custom_123 my-template.json

# 导入模板
./ollama-cli template import my-template.json

# 删除自定义模板
./ollama-cli template delete tmpl_custom_123
```

### 📁 文件操作

文件操作功能帮助你管理提示词文件和处理结果。

```bash
# 创建提示词模板文件
./ollama-cli file template my-prompts.txt

# 验证文件格式
./ollama-cli file validate my-prompts.txt

# 查看文件信息
./ollama-cli file info my-prompts.txt

# 备份重要文件
./ollama-cli file backup my-prompts.txt

# 清理旧文件和任务
./ollama-cli file cleanup
```

### ⚙️ 配置管理

#### 命令行选项
```bash
-config string    配置文件路径
-host string      Ollama服务器地址 (默认: "http://localhost:11434")
-verbose          启用详细输出
-version          显示版本信息
```

#### 配置文件
创建配置文件 `~/.ollama-cli.json`：

```json
{
  "host": "http://localhost:11434",
  "verbose": false,
  "timeout": 30
}
```

使用自定义配置：
```bash
./ollama-cli -config my-config.json list
./ollama-cli -host http://remote-server:11434 list
```

## 🎯 实际使用示例

### 示例1：内容创作工作流

```bash
# 1. 创建专门的创作会话
./ollama-cli sessions create "小说创作" llama2

# 2. 使用创意写作模板
./ollama-cli template use builtin_creative_writing
# 选择类型：故事
# 输入主题：科幻冒险
# 选择风格：现代
# 选择长度：中篇

# 3. 在会话中继续创作
./ollama-cli chat-session session_novel_123

# 4. 导出最终作品
./ollama-cli sessions export session_novel_123 novel-draft.json
```

### 示例2：批量翻译工作流

```bash
# 1. 准备翻译文件
echo "你好，世界
谢谢你的帮助
再见，朋友" > chinese-texts.txt

# 2. 使用翻译模板批量处理
./ollama-cli template use builtin_translate
# 或者直接批处理：
./ollama-cli batch run llama2 chinese-texts.txt

# 3. 导出翻译结果
./ollama-cli batch export batch_123 translations.csv
```

### 示例3：代码审查工作流

```bash
# 1. 准备代码审查任务
./ollama-cli file template code-review-tasks.txt

# 2. 使用代码审查模板
./ollama-cli template use builtin_code_review

# 3. 批量审查多个代码片段
./ollama-cli batch run codellama code-review-tasks.txt

# 4. 生成审查报告
./ollama-cli batch export batch_456 review-report.json
```

## 🏗️ 高级功能

### ⚡ 性能监控

启用详细模式查看性能统计：

```bash
# 查看详细性能信息
./ollama-cli -verbose stream-generate llama2 "写一篇长文章"

# 输出示例：
# ⏱️  耗时: 15.2s | 🔤 Tokens: 342 (22.5/s) | 📝 字符: 1,247 | ❌ 错误: 0
```

### 🔄 流式处理优化

```bash
# 对比普通生成和流式生成的体验
./ollama-cli generate llama2 "长文本任务"        # 等待完整结果
./ollama-cli stream-generate llama2 "长文本任务"  # 实时显示生成过程
```

### 🎛️ 高级配置

```json
{
  "host": "http://localhost:11434",
  "verbose": true,
  "timeout": 60,
  "proxy": "http://proxy:8080",
  "retry_count": 3,
  "max_concurrent": 5
}
```

## 🏗️ 项目架构

### 📁 目录结构
```
ollama-cli/
├── main.go                    # 主程序入口
├── go.mod                     # Go模块定义
├── Makefile                   # 构建脚本
├── README.md                  # 项目文档
├── ARCHITECTURE.md            # 架构文档
├── FEATURES.md                # 功能详解
├── config.example.json        # 配置示例
├── examples/                  # 使用示例
│   ├── basic_usage.go
│   └── config_example.go
└── internal/                  # 内部模块
    ├── cli/                   # CLI应用逻辑
    ├── config/                # 配置管理
    ├── ollama/                # Ollama API客户端
    ├── session/               # 会话管理
    ├── fileops/               # 文件操作
    ├── monitor/               # 性能监控
    ├── templates/             # 模板系统
    ├── errors/                # 错误处理
    └── logger/                # 日志记录
```

### 🧩 核心模块

- **CLI模块** - 命令行界面和用户交互
- **Ollama客户端** - API通信和数据处理
- **会话管理** - 对话历史的持久化存储
- **批处理引擎** - 大规模任务处理
- **模板系统** - 可重用的提示词模板
- **性能监控** - 实时性能指标收集
- **文件操作** - 文件管理和数据导出

## 🔧 故障排除

### 常见问题

#### 1. 连接问题
```bash
# 检查Ollama服务状态
ollama list

# 测试连接
./ollama-cli -verbose list

# 使用自定义服务器地址
./ollama-cli -host http://your-server:11434 list
```

#### 2. 模型问题
```bash
# 确保模型已下载
ollama pull llama2

# 查看可用模型
./ollama-cli list
```

#### 3. 配置问题
```bash
# 验证配置文件
./ollama-cli -config my-config.json -verbose list

# 使用默认配置
./ollama-cli list
```

#### 4. 权限问题
```bash
# Linux/macOS 用户可能需要执行权限
chmod +x ollama-cli

# Windows 用户确保在正确目录
.\ollama-cli.exe list
```

### 调试模式

```bash
# 启用详细输出查看详细信息
./ollama-cli -verbose [command]

# 查看完整错误信息
./ollama-cli -verbose generate llama2 "test" 2>&1 | tee debug.log
```

## 🚀 性能优化建议

### 1. 硬件建议
- **CPU**: 多核处理器，支持批处理并发
- **内存**: 8GB+ RAM，用于大型模型
- **存储**: SSD硬盘，提升文件I/O性能

### 2. 配置优化
```json
{
  "timeout": 120,           // 大模型需要更长超时
  "max_concurrent": 3,      // 根据硬件调整并发数
  "buffer_size": 8192       // 优化网络缓冲区
}
```

### 3. 使用建议
- 对于长文本生成，使用流式模式
- 批处理时合理分组，避免单次任务过大
- 定期清理会话和批处理历史
- 使用模板提高提示词质量

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
go test ./...

# 运行特定模块测试
go test ./internal/config
go test ./internal/ollama
go test ./internal/errors

# 查看测试覆盖率
go test -cover ./...

# 生成覆盖率报告
make test-coverage
```

### 测试覆盖

- ✅ 配置管理 - 100%覆盖
- ✅ API客户端 - 95%覆盖
- ✅ 错误处理 - 100%覆盖
- ✅ 核心功能 - 90%+覆盖



### 开发规范

```bash
# 代码格式化
go fmt ./...

# 代码检查
go vet ./...

# 运行测试
go test ./...

# 构建验证
make build
```

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源协议。

## 🙏 致谢

- [Ollama](https://ollama.ai) - 提供了优秀的本地AI模型运行环境
- [Go语言团队](https://golang.org) - 提供了强大的编程语言和标准库
- 所有贡献者和用户的支持与反馈



<div align="center">

**如果这个项目对你有帮助，请给一个 ⭐ Star！**

[⬆ 回到顶部](#-ollama-cli---专业级ai工作流工具)

</div>

## ❓ 常见问题

### Q: 如何连接到远程Ollama服务器？
**A**: 使用 `-host` 参数或在配置文件中设置：
```bash
./ollama-cli -host http://remote-server:11434 list
```

### Q: 会话数据存储在哪里？
**A**: 默认存储在 `~/.ollama-cli/sessions/` 目录，每个会话一个JSON文件。

### Q: 如何备份我的数据？
**A**:
```bash
# 导出所有会话
./ollama-cli sessions list  # 获取会话ID
./ollama-cli sessions export session_id backup.json

# 备份模板
./ollama-cli template export template_id template-backup.json
```

### Q: 批处理任务失败了怎么办？
**A**:
- 检查 `./ollama-cli batch show task_id` 查看详细错误
- 使用 `-verbose` 模式重新运行
- 确保Ollama服务正常运行

### Q: 如何提高生成速度？
**A**:
- 使用流式模式：`stream-generate`
- 调整超时设置
- 确保硬件资源充足
- 使用合适大小的模型

### Q: 支持哪些文件格式？
**A**:
- **输入**: TXT文件（UTF-8编码）
- **输出**: TXT、JSON、CSV格式
- **配置**: JSON格式
- **模板**: JSON格式

### Q: 如何自定义模板？
**A**:
```bash
# 交互式创建
./ollama-cli template create

# 或导入现有模板
./ollama-cli template import my-template.json
```
