// +build ignore

package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"ollama-cli/internal/ollama"
)

// 这个示例展示了如何直接使用Ollama客户端库
func main() {
	// 创建客户端
	client := ollama.NewClient("http://localhost:11434", 30*time.Second, true)
	ctx := context.Background()

	// 示例1: 列出所有模型
	fmt.Println("=== 列出所有模型 ===")
	models, err := client.ListModels(ctx)
	if err != nil {
		log.Printf("获取模型列表失败: %v", err)
	} else {
		for _, model := range models.Models {
			fmt.Printf("模型: %s, 大小: %d bytes\n", model.Name, model.Size)
		}
	}

	// 示例2: 生成文本
	fmt.Println("\n=== 生成文本示例 ===")
	generateResp, err := client.Generate(ctx, "llama2", "什么是人工智能？", false)
	if err != nil {
		log.Printf("生成文本失败: %v", err)
	} else {
		fmt.Printf("生成的文本: %s\n", generateResp.Response)
	}

	// 示例3: 聊天对话
	fmt.Println("\n=== 聊天对话示例 ===")
	messages := []ollama.ChatMessage{
		{
			Role:    "user",
			Content: "你好，请简单介绍一下自己",
		},
	}

	chatResp, err := client.Chat(ctx, "llama2", messages, false)
	if err != nil {
		log.Printf("聊天失败: %v", err)
	} else {
		fmt.Printf("助手回复: %s\n", chatResp.Message.Content)
	}

	// 示例4: 显示模型信息
	fmt.Println("\n=== 模型信息示例 ===")
	showResp, err := client.ShowModel(ctx, "llama2")
	if err != nil {
		log.Printf("获取模型信息失败: %v", err)
	} else {
		fmt.Printf("模型格式: %s\n", showResp.Details.Format)
		fmt.Printf("模型家族: %s\n", showResp.Details.Family)
		fmt.Printf("参数大小: %s\n", showResp.Details.ParameterSize)
	}
}
