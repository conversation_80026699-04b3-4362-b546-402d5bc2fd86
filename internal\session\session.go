package session

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"ollama-cli/internal/ollama"
)

// Session 聊天会话
type Session struct {
	ID        string                  `json:"id"`
	Name      string                  `json:"name"`
	Model     string                  `json:"model"`
	Messages  []ollama.ChatMessage    `json:"messages"`
	CreatedAt time.Time               `json:"created_at"`
	UpdatedAt time.Time               `json:"updated_at"`
	Metadata  map[string]interface{}  `json:"metadata,omitempty"`
}

// Manager 会话管理器
type Manager struct {
	sessionsDir string
}

// NewManager 创建新的会话管理器
func NewManager(sessionsDir string) *Manager {
	return &Manager{
		sessionsDir: sessionsDir,
	}
}

// CreateSession 创建新会话
func (m *Manager) CreateSession(name, model string) (*Session, error) {
	if err := m.ensureSessionsDir(); err != nil {
		return nil, err
	}

	session := &Session{
		ID:        generateSessionID(),
		Name:      name,
		Model:     model,
		Messages:  make([]ollama.ChatMessage, 0),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Metadata:  make(map[string]interface{}),
	}

	if err := m.SaveSession(session); err != nil {
		return nil, err
	}

	return session, nil
}

// SaveSession 保存会话
func (m *Manager) SaveSession(session *Session) error {
	if err := m.ensureSessionsDir(); err != nil {
		return err
	}

	session.UpdatedAt = time.Now()
	
	filename := filepath.Join(m.sessionsDir, session.ID+".json")
	data, err := json.MarshalIndent(session, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化会话失败: %w", err)
	}

	return os.WriteFile(filename, data, 0644)
}

// LoadSession 加载会话
func (m *Manager) LoadSession(sessionID string) (*Session, error) {
	filename := filepath.Join(m.sessionsDir, sessionID+".json")
	
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取会话文件失败: %w", err)
	}

	var session Session
	if err := json.Unmarshal(data, &session); err != nil {
		return nil, fmt.Errorf("解析会话数据失败: %w", err)
	}

	return &session, nil
}

// ListSessions 列出所有会话
func (m *Manager) ListSessions() ([]*Session, error) {
	if err := m.ensureSessionsDir(); err != nil {
		return nil, err
	}

	files, err := os.ReadDir(m.sessionsDir)
	if err != nil {
		return nil, fmt.Errorf("读取会话目录失败: %w", err)
	}

	var sessions []*Session
	for _, file := range files {
		if !file.IsDir() && filepath.Ext(file.Name()) == ".json" {
			sessionID := file.Name()[:len(file.Name())-5] // 移除.json扩展名
			session, err := m.LoadSession(sessionID)
			if err != nil {
				continue // 跳过损坏的会话文件
			}
			sessions = append(sessions, session)
		}
	}

	return sessions, nil
}

// DeleteSession 删除会话
func (m *Manager) DeleteSession(sessionID string) error {
	filename := filepath.Join(m.sessionsDir, sessionID+".json")
	
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return fmt.Errorf("会话不存在: %s", sessionID)
	}

	return os.Remove(filename)
}

// AddMessage 添加消息到会话
func (m *Manager) AddMessage(sessionID string, message ollama.ChatMessage) error {
	session, err := m.LoadSession(sessionID)
	if err != nil {
		return err
	}

	session.Messages = append(session.Messages, message)
	return m.SaveSession(session)
}

// GetSessionMessages 获取会话消息
func (m *Manager) GetSessionMessages(sessionID string) ([]ollama.ChatMessage, error) {
	session, err := m.LoadSession(sessionID)
	if err != nil {
		return nil, err
	}

	return session.Messages, nil
}

// UpdateSessionName 更新会话名称
func (m *Manager) UpdateSessionName(sessionID, newName string) error {
	session, err := m.LoadSession(sessionID)
	if err != nil {
		return err
	}

	session.Name = newName
	return m.SaveSession(session)
}

// GetSessionsByModel 根据模型获取会话
func (m *Manager) GetSessionsByModel(model string) ([]*Session, error) {
	allSessions, err := m.ListSessions()
	if err != nil {
		return nil, err
	}

	var sessions []*Session
	for _, session := range allSessions {
		if session.Model == model {
			sessions = append(sessions, session)
		}
	}

	return sessions, nil
}

// ensureSessionsDir 确保会话目录存在
func (m *Manager) ensureSessionsDir() error {
	return os.MkdirAll(m.sessionsDir, 0755)
}

// generateSessionID 生成会话ID
func generateSessionID() string {
	return fmt.Sprintf("session_%d", time.Now().UnixNano())
}

// ExportSession 导出会话为JSON文件
func (m *Manager) ExportSession(sessionID, exportPath string) error {
	session, err := m.LoadSession(sessionID)
	if err != nil {
		return err
	}

	data, err := json.MarshalIndent(session, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化会话失败: %w", err)
	}

	return os.WriteFile(exportPath, data, 0644)
}

// ImportSession 从JSON文件导入会话
func (m *Manager) ImportSession(importPath string) (*Session, error) {
	data, err := os.ReadFile(importPath)
	if err != nil {
		return nil, fmt.Errorf("读取导入文件失败: %w", err)
	}

	var session Session
	if err := json.Unmarshal(data, &session); err != nil {
		return nil, fmt.Errorf("解析导入数据失败: %w", err)
	}

	// 生成新的ID和时间戳
	session.ID = generateSessionID()
	session.CreatedAt = time.Now()
	session.UpdatedAt = time.Now()

	if err := m.SaveSession(&session); err != nil {
		return nil, err
	}

	return &session, nil
}

// GetSessionStats 获取会话统计信息
func (m *Manager) GetSessionStats(sessionID string) (map[string]interface{}, error) {
	session, err := m.LoadSession(sessionID)
	if err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"message_count":    len(session.Messages),
		"user_messages":    0,
		"assistant_messages": 0,
		"total_characters": 0,
		"created_at":       session.CreatedAt,
		"updated_at":       session.UpdatedAt,
		"model":           session.Model,
	}

	for _, msg := range session.Messages {
		stats["total_characters"] = stats["total_characters"].(int) + len(msg.Content)
		if msg.Role == "user" {
			stats["user_messages"] = stats["user_messages"].(int) + 1
		} else if msg.Role == "assistant" {
			stats["assistant_messages"] = stats["assistant_messages"].(int) + 1
		}
	}

	return stats, nil
}
