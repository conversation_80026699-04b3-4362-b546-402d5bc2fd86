package cli

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"ollama-cli/internal/config"
	"ollama-cli/internal/fileops"
	"ollama-cli/internal/monitor"
	"ollama-cli/internal/ollama"
	"ollama-cli/internal/session"
	"ollama-cli/internal/templates"
)

// App CLI应用程序
type App struct {
	config          *config.Config
	client          *ollama.Client
	sessionManager  *session.Manager
	monitor         *monitor.Monitor
	fileManager     *fileops.FileManager
	templateManager *templates.Manager
}

// NewApp 创建新的CLI应用
func NewApp(cfg *config.Config) *App {
	timeout := time.Duration(cfg.Timeout) * time.Second
	client := ollama.NewClient(cfg.Host, timeout, cfg.Verbose)

	// 创建会话目录
	homeDir, _ := os.UserHomeDir()
	workDir := filepath.Join(homeDir, ".ollama-cli")
	sessionsDir := filepath.Join(workDir, "sessions")
	templatesDir := filepath.Join(workDir, "templates")

	sessionManager := session.NewManager(sessionsDir)
	fileManager := fileops.NewFileManager(workDir)
	templateManager := templates.NewManager(templatesDir)

	// 初始化内置模板
	templateManager.InitializeBuiltinTemplates()

	return &App{
		config:          cfg,
		client:          client,
		sessionManager:  sessionManager,
		monitor:         monitor.NewMonitor(),
		fileManager:     fileManager,
		templateManager: templateManager,
	}
}

// Run 运行CLI命令
func (a *App) Run(args []string) error {
	if len(args) == 0 {
		return fmt.Errorf("未指定命令")
	}

	command := args[0]
	ctx := context.Background()

	switch command {
	case "list":
		return a.listModels(ctx)
	case "pull":
		if len(args) < 2 {
			return fmt.Errorf("pull命令需要指定模型名称")
		}
		return a.pullModel(ctx, args[1])
	case "chat":
		if len(args) < 2 {
			return fmt.Errorf("chat命令需要指定模型名称")
		}
		return a.chatWithModel(ctx, args[1])
	case "generate":
		if len(args) < 3 {
			return fmt.Errorf("generate命令需要指定模型名称和提示词")
		}
		prompt := strings.Join(args[2:], " ")
		return a.generateText(ctx, args[1], prompt)
	case "stream-generate":
		if len(args) < 3 {
			return fmt.Errorf("stream-generate命令需要指定模型名称和提示词")
		}
		prompt := strings.Join(args[2:], " ")
		return a.streamGenerateText(ctx, args[1], prompt)
	case "show":
		if len(args) < 2 {
			return fmt.Errorf("show命令需要指定模型名称")
		}
		return a.showModel(ctx, args[1])
	case "delete":
		if len(args) < 2 {
			return fmt.Errorf("delete命令需要指定模型名称")
		}
		return a.deleteModel(ctx, args[1])
	case "sessions":
		return a.manageSessions(ctx, args[1:])
	case "chat-session":
		if len(args) < 2 {
			return fmt.Errorf("chat-session命令需要指定会话ID")
		}
		return a.chatWithSession(ctx, args[1])
	case "batch":
		return a.manageBatch(ctx, args[1:])
	case "file":
		return a.manageFiles(ctx, args[1:])
	case "template":
		return a.manageTemplates(ctx, args[1:])
	default:
		return fmt.Errorf("未知命令: %s", command)
	}
}

// listModels 列出所有模型
func (a *App) listModels(ctx context.Context) error {
	resp, err := a.client.ListModels(ctx)
	if err != nil {
		return fmt.Errorf("获取模型列表失败: %w", err)
	}

	if len(resp.Models) == 0 {
		fmt.Println("没有找到任何模型")
		return nil
	}

	fmt.Printf("%-30s %-15s %-20s %s\n", "模型名称", "大小", "修改时间", "摘要")
	fmt.Println(strings.Repeat("-", 80))

	for _, model := range resp.Models {
		size := formatSize(model.Size)
		modTime := model.ModifiedAt.Format("2006-01-02 15:04")
		digest := model.Digest
		if len(digest) > 12 {
			digest = digest[:12] + "..."
		}
		
		fmt.Printf("%-30s %-15s %-20s %s\n", model.Name, size, modTime, digest)
	}

	return nil
}

// pullModel 下载模型
func (a *App) pullModel(ctx context.Context, model string) error {
	fmt.Printf("正在下载模型: %s\n", model)
	return a.client.PullModel(ctx, model, true)
}

// chatWithModel 与模型对话
func (a *App) chatWithModel(ctx context.Context, model string) error {
	fmt.Printf("开始与模型 %s 对话 (输入 'exit' 退出, 'stream' 切换流式模式)\n", model)
	fmt.Println(strings.Repeat("-", 50))

	scanner := bufio.NewScanner(os.Stdin)
	var messages []ollama.ChatMessage
	streamMode := true // 默认使用流式模式

	for {
		fmt.Print("用户: ")
		if !scanner.Scan() {
			break
		}

		input := strings.TrimSpace(scanner.Text())
		if input == "exit" {
			fmt.Println("对话结束")
			break
		}

		if input == "stream" {
			streamMode = !streamMode
			fmt.Printf("流式模式: %v\n", streamMode)
			continue
		}

		if input == "" {
			continue
		}

		// 添加用户消息
		messages = append(messages, ollama.ChatMessage{
			Role:    "user",
			Content: input,
		})

		fmt.Print("助手: ")

		if streamMode {
			// 使用流式聊天
			var fullResponse strings.Builder
			err := a.client.ChatStream(ctx, model, messages, func(resp ollama.ChatResponse) error {
				if resp.Message.Content != "" {
					fmt.Print(resp.Message.Content)
					fullResponse.WriteString(resp.Message.Content)
				}
				return nil
			})

			if err != nil {
				fmt.Printf("\n聊天失败: %v\n", err)
				continue
			}

			fmt.Println() // 换行

			// 添加助手消息到历史
			messages = append(messages, ollama.ChatMessage{
				Role:    "assistant",
				Content: fullResponse.String(),
			})
		} else {
			// 使用普通聊天
			resp, err := a.client.Chat(ctx, model, messages, false)
			if err != nil {
				fmt.Printf("聊天失败: %v\n", err)
				continue
			}

			// 显示助手回复
			fmt.Printf("%s\n", resp.Message.Content)

			// 添加助手消息到历史
			messages = append(messages, resp.Message)
		}

		fmt.Println()
	}

	return scanner.Err()
}

// generateText 生成文本
func (a *App) generateText(ctx context.Context, model, prompt string) error {
	sessionID := fmt.Sprintf("generate_%d", time.Now().UnixNano())
	a.monitor.StartSession(sessionID, model)
	defer func() {
		if metrics := a.monitor.EndSession(sessionID); metrics != nil && a.config.Verbose {
			fmt.Println(strings.Repeat("-", 50))
			a.monitor.PrintMetrics(metrics)
		}
	}()

	fmt.Printf("使用模型 %s 生成文本...\n", model)

	a.monitor.RecordRequest(sessionID)
	resp, err := a.client.Generate(ctx, model, prompt, false)
	if err != nil {
		a.monitor.RecordError(sessionID)
		return fmt.Errorf("生成文本失败: %w", err)
	}

	// 记录性能数据
	a.monitor.RecordTokens(sessionID, resp.PromptEvalCount, resp.EvalCount)
	a.monitor.RecordText(sessionID, resp.Response)

	fmt.Println("生成结果:")
	fmt.Println(strings.Repeat("-", 50))
	fmt.Println(resp.Response)

	if a.config.Verbose && resp.Done {
		fmt.Println(strings.Repeat("-", 50))
		fmt.Printf("API统计信息:\n")
		fmt.Printf("  总耗时: %s\n", time.Duration(resp.TotalDuration))
		fmt.Printf("  加载耗时: %s\n", time.Duration(resp.LoadDuration))
		if resp.PromptEvalCount > 0 {
			fmt.Printf("  提示词评估: %d tokens, %s\n", resp.PromptEvalCount, time.Duration(resp.PromptEvalDuration))
		}
		if resp.EvalCount > 0 {
			fmt.Printf("  生成评估: %d tokens, %s\n", resp.EvalCount, time.Duration(resp.EvalDuration))
		}
	}

	return nil
}

// showModel 显示模型信息
func (a *App) showModel(ctx context.Context, model string) error {
	resp, err := a.client.ShowModel(ctx, model)
	if err != nil {
		return fmt.Errorf("获取模型信息失败: %w", err)
	}

	fmt.Printf("模型信息: %s\n", model)
	fmt.Println(strings.Repeat("=", 50))
	
	fmt.Printf("格式: %s\n", resp.Details.Format)
	fmt.Printf("家族: %s\n", resp.Details.Family)
	fmt.Printf("参数大小: %s\n", resp.Details.ParameterSize)
	fmt.Printf("量化级别: %s\n", resp.Details.QuantizationLevel)
	
	if resp.License != "" {
		fmt.Printf("\n许可证:\n%s\n", resp.License)
	}
	
	if resp.Template != "" {
		fmt.Printf("\n模板:\n%s\n", resp.Template)
	}
	
	if a.config.Verbose && resp.Modelfile != "" {
		fmt.Printf("\nModelfile:\n%s\n", resp.Modelfile)
	}

	return nil
}

// deleteModel 删除模型
func (a *App) deleteModel(ctx context.Context, model string) error {
	fmt.Printf("确定要删除模型 %s 吗? (y/N): ", model)
	
	scanner := bufio.NewScanner(os.Stdin)
	if !scanner.Scan() {
		return fmt.Errorf("读取输入失败")
	}
	
	input := strings.ToLower(strings.TrimSpace(scanner.Text()))
	if input != "y" && input != "yes" {
		fmt.Println("取消删除")
		return nil
	}

	return a.client.DeleteModel(ctx, model)
}

// formatSize 格式化文件大小
func formatSize(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// streamGenerateText 流式生成文本
func (a *App) streamGenerateText(ctx context.Context, model, prompt string) error {
	sessionID := fmt.Sprintf("stream_generate_%d", time.Now().UnixNano())
	a.monitor.StartSession(sessionID, model)
	realtimeMonitor := monitor.NewRealtimeMonitor(a.monitor, sessionID)

	defer func() {
		if metrics := a.monitor.EndSession(sessionID); metrics != nil {
			fmt.Println()
			if a.config.Verbose {
				fmt.Println(strings.Repeat("-", 50))
				a.monitor.PrintMetrics(metrics)
			} else {
				a.monitor.PrintCompactMetrics(metrics)
			}
		}
	}()

	fmt.Printf("使用模型 %s 流式生成文本...\n", model)
	fmt.Println("生成结果:")
	fmt.Println(strings.Repeat("-", 50))

	var fullResponse strings.Builder

	a.monitor.RecordRequest(sessionID)
	err := a.client.GenerateStream(ctx, model, prompt, func(resp ollama.GenerateResponse) error {
		if resp.Response != "" {
			fmt.Print(resp.Response)
			fullResponse.WriteString(resp.Response)
			realtimeMonitor.RecordChunk(resp.Response)
		}

		if resp.Done {
			a.monitor.RecordTokens(sessionID, resp.PromptEvalCount, resp.EvalCount)
		}

		return nil
	})

	if err != nil {
		a.monitor.RecordError(sessionID)
		return fmt.Errorf("流式生成文本失败: %w", err)
	}

	return nil
}

// manageSessions 管理会话
func (a *App) manageSessions(ctx context.Context, args []string) error {
	if len(args) == 0 {
		return a.listSessions(ctx)
	}

	subCommand := args[0]
	switch subCommand {
	case "list":
		return a.listSessions(ctx)
	case "create":
		if len(args) < 3 {
			return fmt.Errorf("sessions create命令需要指定会话名称和模型")
		}
		return a.createSession(ctx, args[1], args[2])
	case "delete":
		if len(args) < 2 {
			return fmt.Errorf("sessions delete命令需要指定会话ID")
		}
		return a.deleteSession(ctx, args[1])
	case "show":
		if len(args) < 2 {
			return fmt.Errorf("sessions show命令需要指定会话ID")
		}
		return a.showSession(ctx, args[1])
	case "export":
		if len(args) < 3 {
			return fmt.Errorf("sessions export命令需要指定会话ID和导出路径")
		}
		return a.exportSession(ctx, args[1], args[2])
	case "import":
		if len(args) < 2 {
			return fmt.Errorf("sessions import命令需要指定导入文件路径")
		}
		return a.importSession(ctx, args[1])
	case "stats":
		if len(args) < 2 {
			return fmt.Errorf("sessions stats命令需要指定会话ID")
		}
		return a.showSessionStats(ctx, args[1])
	default:
		return fmt.Errorf("未知的会话子命令: %s", subCommand)
	}
}

// listSessions 列出所有会话
func (a *App) listSessions(ctx context.Context) error {
	sessions, err := a.sessionManager.ListSessions()
	if err != nil {
		return fmt.Errorf("获取会话列表失败: %w", err)
	}

	if len(sessions) == 0 {
		fmt.Println("没有找到任何会话")
		return nil
	}

	fmt.Printf("%-20s %-30s %-15s %-10s %-20s\n", "会话ID", "名称", "模型", "消息数", "最后更新")
	fmt.Println(strings.Repeat("-", 100))

	for _, sess := range sessions {
		shortID := sess.ID
		if len(shortID) > 18 {
			shortID = shortID[:18] + ".."
		}

		name := sess.Name
		if len(name) > 28 {
			name = name[:28] + ".."
		}

		updateTime := sess.UpdatedAt.Format("2006-01-02 15:04")

		fmt.Printf("%-20s %-30s %-15s %-10d %-20s\n",
			shortID, name, sess.Model, len(sess.Messages), updateTime)
	}

	return nil
}

// createSession 创建新会话
func (a *App) createSession(ctx context.Context, name, model string) error {
	sess, err := a.sessionManager.CreateSession(name, model)
	if err != nil {
		return fmt.Errorf("创建会话失败: %w", err)
	}

	fmt.Printf("会话创建成功:\n")
	fmt.Printf("  ID: %s\n", sess.ID)
	fmt.Printf("  名称: %s\n", sess.Name)
	fmt.Printf("  模型: %s\n", sess.Model)
	fmt.Printf("  创建时间: %s\n", sess.CreatedAt.Format("2006-01-02 15:04:05"))

	return nil
}

// deleteSession 删除会话
func (a *App) deleteSession(ctx context.Context, sessionID string) error {
	// 确认删除
	fmt.Printf("确定要删除会话 %s 吗? (y/N): ", sessionID)

	scanner := bufio.NewScanner(os.Stdin)
	if !scanner.Scan() {
		return fmt.Errorf("读取输入失败")
	}

	input := strings.ToLower(strings.TrimSpace(scanner.Text()))
	if input != "y" && input != "yes" {
		fmt.Println("取消删除")
		return nil
	}

	err := a.sessionManager.DeleteSession(sessionID)
	if err != nil {
		return fmt.Errorf("删除会话失败: %w", err)
	}

	fmt.Printf("会话 %s 删除成功\n", sessionID)
	return nil
}

// showSession 显示会话详情
func (a *App) showSession(ctx context.Context, sessionID string) error {
	sess, err := a.sessionManager.LoadSession(sessionID)
	if err != nil {
		return fmt.Errorf("加载会话失败: %w", err)
	}

	fmt.Printf("会话详情:\n")
	fmt.Printf("  ID: %s\n", sess.ID)
	fmt.Printf("  名称: %s\n", sess.Name)
	fmt.Printf("  模型: %s\n", sess.Model)
	fmt.Printf("  创建时间: %s\n", sess.CreatedAt.Format("2006-01-02 15:04:05"))
	fmt.Printf("  更新时间: %s\n", sess.UpdatedAt.Format("2006-01-02 15:04:05"))
	fmt.Printf("  消息数量: %d\n", len(sess.Messages))

	if len(sess.Messages) > 0 {
		fmt.Println("\n最近的消息:")
		fmt.Println(strings.Repeat("-", 50))

		// 显示最后几条消息
		start := len(sess.Messages) - 5
		if start < 0 {
			start = 0
		}

		for i := start; i < len(sess.Messages); i++ {
			msg := sess.Messages[i]
			content := msg.Content
			if len(content) > 100 {
				content = content[:100] + "..."
			}
			fmt.Printf("%s: %s\n", msg.Role, content)
		}
	}

	return nil
}

// exportSession 导出会话
func (a *App) exportSession(ctx context.Context, sessionID, exportPath string) error {
	err := a.sessionManager.ExportSession(sessionID, exportPath)
	if err != nil {
		return fmt.Errorf("导出会话失败: %w", err)
	}

	fmt.Printf("会话 %s 导出成功: %s\n", sessionID, exportPath)
	return nil
}

// importSession 导入会话
func (a *App) importSession(ctx context.Context, importPath string) error {
	sess, err := a.sessionManager.ImportSession(importPath)
	if err != nil {
		return fmt.Errorf("导入会话失败: %w", err)
	}

	fmt.Printf("会话导入成功:\n")
	fmt.Printf("  新ID: %s\n", sess.ID)
	fmt.Printf("  名称: %s\n", sess.Name)
	fmt.Printf("  模型: %s\n", sess.Model)

	return nil
}

// showSessionStats 显示会话统计
func (a *App) showSessionStats(ctx context.Context, sessionID string) error {
	stats, err := a.sessionManager.GetSessionStats(sessionID)
	if err != nil {
		return fmt.Errorf("获取会话统计失败: %w", err)
	}

	fmt.Printf("会话统计 (%s):\n", sessionID)
	fmt.Printf("  模型: %s\n", stats["model"])
	fmt.Printf("  总消息数: %d\n", stats["message_count"])
	fmt.Printf("  用户消息: %d\n", stats["user_messages"])
	fmt.Printf("  助手消息: %d\n", stats["assistant_messages"])
	fmt.Printf("  总字符数: %d\n", stats["total_characters"])
	fmt.Printf("  创建时间: %s\n", stats["created_at"].(time.Time).Format("2006-01-02 15:04:05"))
	fmt.Printf("  更新时间: %s\n", stats["updated_at"].(time.Time).Format("2006-01-02 15:04:05"))

	return nil
}

// chatWithSession 使用会话进行对话
func (a *App) chatWithSession(ctx context.Context, sessionID string) error {
	sess, err := a.sessionManager.LoadSession(sessionID)
	if err != nil {
		return fmt.Errorf("加载会话失败: %w", err)
	}

	fmt.Printf("继续会话 '%s' (模型: %s)\n", sess.Name, sess.Model)
	fmt.Printf("输入 'exit' 退出, 'stream' 切换流式模式\n")
	fmt.Println(strings.Repeat("-", 50))

	// 显示会话历史（最后几条消息）
	if len(sess.Messages) > 0 {
		fmt.Println("会话历史:")
		start := len(sess.Messages) - 4
		if start < 0 {
			start = 0
		}

		for i := start; i < len(sess.Messages); i++ {
			msg := sess.Messages[i]
			role := "用户"
			if msg.Role == "assistant" {
				role = "助手"
			}
			fmt.Printf("%s: %s\n", role, msg.Content)
		}
		fmt.Println(strings.Repeat("-", 50))
	}

	scanner := bufio.NewScanner(os.Stdin)
	streamMode := true

	for {
		fmt.Print("用户: ")
		if !scanner.Scan() {
			break
		}

		input := strings.TrimSpace(scanner.Text())
		if input == "exit" {
			fmt.Println("对话结束")
			break
		}

		if input == "stream" {
			streamMode = !streamMode
			fmt.Printf("流式模式: %v\n", streamMode)
			continue
		}

		if input == "" {
			continue
		}

		// 添加用户消息到会话
		userMsg := ollama.ChatMessage{
			Role:    "user",
			Content: input,
		}
		sess.Messages = append(sess.Messages, userMsg)

		fmt.Print("助手: ")

		if streamMode {
			// 使用流式聊天
			var fullResponse strings.Builder
			err := a.client.ChatStream(ctx, sess.Model, sess.Messages, func(resp ollama.ChatResponse) error {
				if resp.Message.Content != "" {
					fmt.Print(resp.Message.Content)
					fullResponse.WriteString(resp.Message.Content)
				}
				return nil
			})

			if err != nil {
				fmt.Printf("\n聊天失败: %v\n", err)
				continue
			}

			fmt.Println() // 换行

			// 添加助手消息到会话
			assistantMsg := ollama.ChatMessage{
				Role:    "assistant",
				Content: fullResponse.String(),
			}
			sess.Messages = append(sess.Messages, assistantMsg)
		} else {
			// 使用普通聊天
			resp, err := a.client.Chat(ctx, sess.Model, sess.Messages, false)
			if err != nil {
				fmt.Printf("聊天失败: %v\n", err)
				continue
			}

			fmt.Printf("%s\n", resp.Message.Content)
			sess.Messages = append(sess.Messages, resp.Message)
		}

		// 保存会话
		if err := a.sessionManager.SaveSession(sess); err != nil {
			fmt.Printf("保存会话失败: %v\n", err)
		}

		fmt.Println()
	}

	return scanner.Err()
}

// manageBatch 管理批处理任务
func (a *App) manageBatch(ctx context.Context, args []string) error {
	if len(args) == 0 {
		return a.listBatchJobs(ctx)
	}

	subCommand := args[0]
	switch subCommand {
	case "list":
		return a.listBatchJobs(ctx)
	case "run":
		if len(args) < 3 {
			return fmt.Errorf("batch run命令需要指定模型和输入文件")
		}
		return a.runBatchJob(ctx, args[1], args[2])
	case "show":
		if len(args) < 2 {
			return fmt.Errorf("batch show命令需要指定任务ID")
		}
		return a.showBatchJob(ctx, args[1])
	case "export":
		if len(args) < 3 {
			return fmt.Errorf("batch export命令需要指定任务ID和输出文件")
		}
		return a.exportBatchJob(ctx, args[1], args[2])
	default:
		return fmt.Errorf("未知的批处理子命令: %s", subCommand)
	}
}

// manageFiles 管理文件操作
func (a *App) manageFiles(ctx context.Context, args []string) error {
	if len(args) == 0 {
		return fmt.Errorf("file命令需要指定子命令")
	}

	subCommand := args[0]
	switch subCommand {
	case "template":
		if len(args) < 2 {
			return fmt.Errorf("file template命令需要指定文件名")
		}
		return a.createTemplate(ctx, args[1])
	case "validate":
		if len(args) < 2 {
			return fmt.Errorf("file validate命令需要指定文件名")
		}
		return a.validateFile(ctx, args[1])
	case "info":
		if len(args) < 2 {
			return fmt.Errorf("file info命令需要指定文件名")
		}
		return a.showFileInfo(ctx, args[1])
	case "backup":
		if len(args) < 2 {
			return fmt.Errorf("file backup命令需要指定文件名")
		}
		return a.backupFile(ctx, args[1])
	case "cleanup":
		return a.cleanupFiles(ctx)
	default:
		return fmt.Errorf("未知的文件子命令: %s", subCommand)
	}
}

// listBatchJobs 列出批处理任务
func (a *App) listBatchJobs(ctx context.Context) error {
	jobs, err := a.fileManager.ListBatchJobs()
	if err != nil {
		return fmt.Errorf("获取批处理任务列表失败: %w", err)
	}

	if len(jobs) == 0 {
		fmt.Println("没有找到任何批处理任务")
		return nil
	}

	fmt.Printf("%-20s %-15s %-10s %-10s %-10s %-20s\n", "任务ID", "模型", "状态", "提示数", "错误数", "创建时间")
	fmt.Println(strings.Repeat("-", 100))

	for _, job := range jobs {
		shortID := job.ID
		if len(shortID) > 18 {
			shortID = shortID[:18] + ".."
		}

		createTime := job.CreatedAt.Format("2006-01-02 15:04")

		fmt.Printf("%-20s %-15s %-10s %-10d %-10d %-20s\n",
			shortID, job.Model, job.Status, len(job.Prompts), job.ErrorCount, createTime)
	}

	return nil
}

// runBatchJob 运行批处理任务
func (a *App) runBatchJob(ctx context.Context, model, inputFile string) error {
	// 读取提示词
	prompts, err := a.fileManager.ReadPromptsFromFile(inputFile)
	if err != nil {
		return fmt.Errorf("读取输入文件失败: %w", err)
	}

	if len(prompts) == 0 {
		return fmt.Errorf("输入文件中没有找到有效的提示词")
	}

	// 创建批处理任务
	job := a.fileManager.CreateBatchJob(model, prompts)
	job.Status = "running"

	fmt.Printf("开始批处理任务: %s\n", job.ID)
	fmt.Printf("模型: %s\n", model)
	fmt.Printf("提示词数量: %d\n", len(prompts))
	fmt.Println(strings.Repeat("-", 50))

	// 创建输出目录
	outputDir, err := a.fileManager.CreateOutputDirectory("batch_" + job.ID)
	if err != nil {
		return fmt.Errorf("创建输出目录失败: %w", err)
	}

	// 处理每个提示词
	for i, prompt := range prompts {
		fmt.Printf("处理 %d/%d: ", i+1, len(prompts))

		sessionID := fmt.Sprintf("batch_%s_%d", job.ID, i)
		a.monitor.StartSession(sessionID, model)

		a.monitor.RecordRequest(sessionID)
		resp, err := a.client.Generate(ctx, model, prompt, false)
		if err != nil {
			fmt.Printf("失败 - %v\n", err)
			job.ErrorCount++
			job.Results = append(job.Results, fmt.Sprintf("错误: %v", err))
			a.monitor.RecordError(sessionID)
		} else {
			fmt.Printf("完成\n")
			job.Results = append(job.Results, resp.Response)
			a.monitor.RecordTokens(sessionID, resp.PromptEvalCount, resp.EvalCount)
			a.monitor.RecordText(sessionID, resp.Response)
		}

		a.monitor.EndSession(sessionID)

		// 保存单个结果
		resultFile := filepath.Join(outputDir, fmt.Sprintf("result_%d.txt", i+1))
		if len(job.Results) > i {
			os.WriteFile(resultFile, []byte(job.Results[i]), 0644)
		}
	}

	// 更新任务状态
	job.Status = "completed"
	job.CompletedAt = time.Now()

	// 保存任务
	if err := a.fileManager.SaveBatchJob(job); err != nil {
		fmt.Printf("保存任务失败: %v\n", err)
	}

	// 保存汇总结果
	summaryFile := filepath.Join(outputDir, "summary.txt")
	a.fileManager.WriteResultsToFile(summaryFile, job.Results)

	// 导出为CSV
	csvFile := filepath.Join(outputDir, "results.csv")
	a.fileManager.ExportToCSV(csvFile, job.Prompts, job.Results)

	// 保存JSON格式
	jsonFile := filepath.Join(outputDir, "job.json")
	a.fileManager.WriteResultsToJSON(jsonFile, job)

	fmt.Println(strings.Repeat("-", 50))
	fmt.Printf("批处理任务完成!\n")
	fmt.Printf("任务ID: %s\n", job.ID)
	fmt.Printf("成功: %d/%d\n", len(prompts)-job.ErrorCount, len(prompts))
	fmt.Printf("输出目录: %s\n", outputDir)

	return nil
}

// showBatchJob 显示批处理任务详情
func (a *App) showBatchJob(ctx context.Context, jobID string) error {
	job, err := a.fileManager.LoadBatchJob(jobID)
	if err != nil {
		return fmt.Errorf("加载任务失败: %w", err)
	}

	fmt.Printf("批处理任务详情:\n")
	fmt.Printf("  ID: %s\n", job.ID)
	fmt.Printf("  模型: %s\n", job.Model)
	fmt.Printf("  状态: %s\n", job.Status)
	fmt.Printf("  提示词数量: %d\n", len(job.Prompts))
	fmt.Printf("  结果数量: %d\n", len(job.Results))
	fmt.Printf("  错误数量: %d\n", job.ErrorCount)
	fmt.Printf("  创建时间: %s\n", job.CreatedAt.Format("2006-01-02 15:04:05"))

	if !job.CompletedAt.IsZero() {
		fmt.Printf("  完成时间: %s\n", job.CompletedAt.Format("2006-01-02 15:04:05"))
		duration := job.CompletedAt.Sub(job.CreatedAt)
		fmt.Printf("  总耗时: %s\n", duration)
	}

	if len(job.Results) > 0 {
		fmt.Println("\n最近的结果:")
		fmt.Println(strings.Repeat("-", 50))

		start := len(job.Results) - 3
		if start < 0 {
			start = 0
		}

		for i := start; i < len(job.Results); i++ {
			result := job.Results[i]
			if len(result) > 100 {
				result = result[:100] + "..."
			}
			fmt.Printf("结果 %d: %s\n", i+1, result)
		}
	}

	return nil
}

// exportBatchJob 导出批处理任务结果
func (a *App) exportBatchJob(ctx context.Context, jobID, outputFile string) error {
	job, err := a.fileManager.LoadBatchJob(jobID)
	if err != nil {
		return fmt.Errorf("加载任务失败: %w", err)
	}

	ext := strings.ToLower(filepath.Ext(outputFile))
	switch ext {
	case ".json":
		err = a.fileManager.WriteResultsToJSON(outputFile, job)
	case ".csv":
		err = a.fileManager.ExportToCSV(outputFile, job.Prompts, job.Results)
	case ".txt":
		err = a.fileManager.WriteResultsToFile(outputFile, job.Results)
	default:
		return fmt.Errorf("不支持的文件格式: %s", ext)
	}

	if err != nil {
		return fmt.Errorf("导出失败: %w", err)
	}

	fmt.Printf("任务 %s 导出成功: %s\n", jobID, outputFile)
	return nil
}

// createTemplate 创建提示词模板
func (a *App) createTemplate(ctx context.Context, filename string) error {
	err := a.fileManager.CreatePromptTemplate(filename)
	if err != nil {
		return fmt.Errorf("创建模板失败: %w", err)
	}

	fmt.Printf("提示词模板创建成功: %s\n", filename)
	return nil
}

// validateFile 验证文件
func (a *App) validateFile(ctx context.Context, filename string) error {
	err := a.fileManager.ValidateFile(filename)
	if err != nil {
		return fmt.Errorf("文件验证失败: %w", err)
	}

	fmt.Printf("文件验证通过: %s\n", filename)
	return nil
}

// showFileInfo 显示文件信息
func (a *App) showFileInfo(ctx context.Context, filename string) error {
	info, err := a.fileManager.GetFileInfo(filename)
	if err != nil {
		return fmt.Errorf("获取文件信息失败: %w", err)
	}

	fmt.Printf("文件信息:\n")
	fmt.Printf("  文件名: %s\n", info["filename"])
	fmt.Printf("  大小: %d 字节\n", info["size"])
	fmt.Printf("  修改时间: %s\n", info["modified"].(time.Time).Format("2006-01-02 15:04:05"))
	fmt.Printf("  提示词数量: %d\n", info["prompt_count"])

	return nil
}

// backupFile 备份文件
func (a *App) backupFile(ctx context.Context, filename string) error {
	backupName, err := a.fileManager.BackupFile(filename)
	if err != nil {
		return fmt.Errorf("备份文件失败: %w", err)
	}

	fmt.Printf("文件备份成功: %s\n", backupName)
	return nil
}

// cleanupFiles 清理文件
func (a *App) cleanupFiles(ctx context.Context) error {
	// 清理30天前的批处理任务
	err := a.fileManager.CleanupOldJobs(30 * 24 * time.Hour)
	if err != nil {
		return fmt.Errorf("清理文件失败: %w", err)
	}

	return nil
}

// manageTemplates 管理模板
func (a *App) manageTemplates(ctx context.Context, args []string) error {
	if len(args) == 0 {
		return a.listTemplates(ctx)
	}

	subCommand := args[0]
	switch subCommand {
	case "list":
		return a.listTemplates(ctx)
	case "show":
		if len(args) < 2 {
			return fmt.Errorf("template show命令需要指定模板ID")
		}
		return a.showTemplate(ctx, args[1])
	case "use":
		if len(args) < 2 {
			return fmt.Errorf("template use命令需要指定模板ID")
		}
		return a.useTemplate(ctx, args[1], args[2:])
	case "create":
		return a.createTemplateInteractive(ctx)
	case "delete":
		if len(args) < 2 {
			return fmt.Errorf("template delete命令需要指定模板ID")
		}
		return a.deleteTemplate(ctx, args[1])
	case "search":
		if len(args) < 2 {
			return fmt.Errorf("template search命令需要指定搜索关键词")
		}
		return a.searchTemplates(ctx, args[1])
	case "export":
		if len(args) < 3 {
			return fmt.Errorf("template export命令需要指定模板ID和导出路径")
		}
		return a.exportTemplate(ctx, args[1], args[2])
	case "import":
		if len(args) < 2 {
			return fmt.Errorf("template import命令需要指定导入文件路径")
		}
		return a.importTemplate(ctx, args[1])
	default:
		return fmt.Errorf("未知的模板子命令: %s", subCommand)
	}
}

// listTemplates 列出所有模板
func (a *App) listTemplates(ctx context.Context) error {
	templateList, err := a.templateManager.ListTemplates()
	if err != nil {
		return fmt.Errorf("获取模板列表失败: %w", err)
	}

	if len(templateList) == 0 {
		fmt.Println("没有找到任何模板")
		return nil
	}

	// 按分类分组
	categories := make(map[string][]*templates.Template)
	for _, tmpl := range templateList {
		categories[tmpl.Category] = append(categories[tmpl.Category], tmpl)
	}

	for category, tmpls := range categories {
		fmt.Printf("\n=== %s ===\n", category)
		fmt.Printf("%-20s %-30s %s\n", "ID", "名称", "描述")
		fmt.Println(strings.Repeat("-", 80))

		for _, tmpl := range tmpls {
			shortID := tmpl.ID
			if len(shortID) > 18 {
				shortID = shortID[:18] + ".."
			}

			name := tmpl.Name
			if len(name) > 28 {
				name = name[:28] + ".."
			}

			description := tmpl.Description
			if len(description) > 30 {
				description = description[:30] + "..."
			}

			fmt.Printf("%-20s %-30s %s\n", shortID, name, description)
		}
	}

	return nil
}

// showTemplate 显示模板详情
func (a *App) showTemplate(ctx context.Context, templateID string) error {
	tmpl, err := a.templateManager.LoadTemplate(templateID)
	if err != nil {
		return fmt.Errorf("加载模板失败: %w", err)
	}

	fmt.Printf("模板详情:\n")
	fmt.Printf("  ID: %s\n", tmpl.ID)
	fmt.Printf("  名称: %s\n", tmpl.Name)
	fmt.Printf("  描述: %s\n", tmpl.Description)
	fmt.Printf("  分类: %s\n", tmpl.Category)
	fmt.Printf("  创建时间: %s\n", tmpl.CreatedAt.Format("2006-01-02 15:04:05"))
	fmt.Printf("  更新时间: %s\n", tmpl.UpdatedAt.Format("2006-01-02 15:04:05"))

	if len(tmpl.Tags) > 0 {
		fmt.Printf("  标签: %s\n", strings.Join(tmpl.Tags, ", "))
	}

	fmt.Printf("\n模板内容:\n")
	fmt.Println(strings.Repeat("-", 50))
	fmt.Println(tmpl.Content)

	if len(tmpl.Variables) > 0 {
		fmt.Printf("\n变量:\n")
		fmt.Println(strings.Repeat("-", 50))
		for _, variable := range tmpl.Variables {
			required := ""
			if variable.Required {
				required = " (必需)"
			}
			fmt.Printf("  %s (%s)%s: %s\n", variable.Name, variable.Type, required, variable.Description)
			if variable.Default != "" {
				fmt.Printf("    默认值: %s\n", variable.Default)
			}
			if len(variable.Choices) > 0 {
				fmt.Printf("    选项: %s\n", strings.Join(variable.Choices, ", "))
			}
		}
	}

	if len(tmpl.Examples) > 0 {
		fmt.Printf("\n示例:\n")
		fmt.Println(strings.Repeat("-", 50))
		for i, example := range tmpl.Examples {
			fmt.Printf("示例 %d: %s\n", i+1, example.Name)
			fmt.Printf("  描述: %s\n", example.Description)
			fmt.Printf("  变量: %v\n", example.Variables)
			if example.Expected != "" {
				fmt.Printf("  期望结果: %s\n", example.Expected)
			}
			fmt.Println()
		}
	}

	return nil
}

// useTemplate 使用模板
func (a *App) useTemplate(ctx context.Context, templateID string, args []string) error {
	tmpl, err := a.templateManager.LoadTemplate(templateID)
	if err != nil {
		return fmt.Errorf("加载模板失败: %w", err)
	}

	fmt.Printf("使用模板: %s\n", tmpl.Name)
	fmt.Printf("描述: %s\n", tmpl.Description)
	fmt.Println(strings.Repeat("-", 50))

	// 收集变量值
	variables := make(map[string]string)
	scanner := bufio.NewScanner(os.Stdin)

	for _, variable := range tmpl.Variables {
		for {
			prompt := fmt.Sprintf("%s (%s)", variable.Name, variable.Description)
			if variable.Default != "" {
				prompt += fmt.Sprintf(" [默认: %s]", variable.Default)
			}
			if len(variable.Choices) > 0 {
				prompt += fmt.Sprintf(" [选项: %s]", strings.Join(variable.Choices, ", "))
			}
			if variable.Required {
				prompt += " *"
			}
			prompt += ": "

			fmt.Print(prompt)
			if !scanner.Scan() {
				return fmt.Errorf("读取输入失败")
			}

			input := strings.TrimSpace(scanner.Text())

			// 使用默认值
			if input == "" && variable.Default != "" {
				input = variable.Default
			}

			// 验证必需字段
			if input == "" && variable.Required {
				fmt.Println("此字段为必需字段，请输入值")
				continue
			}

			// 验证选择项
			if len(variable.Choices) > 0 && input != "" {
				valid := false
				for _, choice := range variable.Choices {
					if input == choice {
						valid = true
						break
					}
				}
				if !valid {
					fmt.Printf("无效选择，请从以下选项中选择: %s\n", strings.Join(variable.Choices, ", "))
					continue
				}
			}

			variables[variable.Name] = input
			break
		}
	}

	// 渲染模板
	rendered, err := a.templateManager.RenderTemplate(templateID, variables)
	if err != nil {
		return fmt.Errorf("渲染模板失败: %w", err)
	}

	fmt.Println("\n渲染结果:")
	fmt.Println(strings.Repeat("=", 50))
	fmt.Println(rendered)
	fmt.Println(strings.Repeat("=", 50))

	// 询问是否要使用此提示词生成内容
	fmt.Print("\n是否要使用此提示词生成内容? (y/N): ")
	if !scanner.Scan() {
		return nil
	}

	if strings.ToLower(strings.TrimSpace(scanner.Text())) == "y" {
		fmt.Print("请选择模型: ")
		if !scanner.Scan() {
			return nil
		}

		model := strings.TrimSpace(scanner.Text())
		if model == "" {
			fmt.Println("未指定模型，取消生成")
			return nil
		}

		return a.generateText(ctx, model, rendered)
	}

	return nil
}

// searchTemplates 搜索模板
func (a *App) searchTemplates(ctx context.Context, query string) error {
	templateList, err := a.templateManager.SearchTemplates(query)
	if err != nil {
		return fmt.Errorf("搜索模板失败: %w", err)
	}

	if len(templateList) == 0 {
		fmt.Printf("没有找到匹配 '%s' 的模板\n", query)
		return nil
	}

	fmt.Printf("搜索结果 ('%s'):\n", query)
	fmt.Printf("%-20s %-30s %-15s %s\n", "ID", "名称", "分类", "描述")
	fmt.Println(strings.Repeat("-", 90))

	for _, tmpl := range templateList {
		shortID := tmpl.ID
		if len(shortID) > 18 {
			shortID = shortID[:18] + ".."
		}

		name := tmpl.Name
		if len(name) > 28 {
			name = name[:28] + ".."
		}

		description := tmpl.Description
		if len(description) > 30 {
			description = description[:30] + "..."
		}

		fmt.Printf("%-20s %-30s %-15s %s\n", shortID, name, tmpl.Category, description)
	}

	return nil
}

// createTemplateInteractive 交互式创建模板
func (a *App) createTemplateInteractive(ctx context.Context) error {
	scanner := bufio.NewScanner(os.Stdin)

	fmt.Println("创建新模板:")
	fmt.Println(strings.Repeat("-", 30))

	fmt.Print("模板名称: ")
	if !scanner.Scan() {
		return fmt.Errorf("读取输入失败")
	}
	name := strings.TrimSpace(scanner.Text())

	fmt.Print("描述: ")
	if !scanner.Scan() {
		return fmt.Errorf("读取输入失败")
	}
	description := strings.TrimSpace(scanner.Text())

	fmt.Print("分类: ")
	if !scanner.Scan() {
		return fmt.Errorf("读取输入失败")
	}
	category := strings.TrimSpace(scanner.Text())

	fmt.Println("模板内容 (输入END结束):")
	var contentLines []string
	for scanner.Scan() {
		line := scanner.Text()
		if line == "END" {
			break
		}
		contentLines = append(contentLines, line)
	}
	content := strings.Join(contentLines, "\n")

	// 简单的变量定义
	var variables []templates.TemplateVariable
	fmt.Println("定义变量 (输入空行结束):")
	for {
		fmt.Print("变量名: ")
		if !scanner.Scan() {
			break
		}
		varName := strings.TrimSpace(scanner.Text())
		if varName == "" {
			break
		}

		fmt.Print("变量描述: ")
		if !scanner.Scan() {
			break
		}
		varDesc := strings.TrimSpace(scanner.Text())

		fmt.Print("是否必需 (y/N): ")
		if !scanner.Scan() {
			break
		}
		required := strings.ToLower(strings.TrimSpace(scanner.Text())) == "y"

		variables = append(variables, templates.TemplateVariable{
			Name:        varName,
			Type:        "string",
			Description: varDesc,
			Required:    required,
		})
	}

	tmpl, err := a.templateManager.CreateTemplate(name, description, category, content, variables)
	if err != nil {
		return fmt.Errorf("创建模板失败: %w", err)
	}

	fmt.Printf("模板创建成功: %s (ID: %s)\n", tmpl.Name, tmpl.ID)
	return nil
}

// deleteTemplate 删除模板
func (a *App) deleteTemplate(ctx context.Context, templateID string) error {
	// 确认删除
	fmt.Printf("确定要删除模板 %s 吗? (y/N): ", templateID)

	scanner := bufio.NewScanner(os.Stdin)
	if !scanner.Scan() {
		return fmt.Errorf("读取输入失败")
	}

	input := strings.ToLower(strings.TrimSpace(scanner.Text()))
	if input != "y" && input != "yes" {
		fmt.Println("取消删除")
		return nil
	}

	err := a.templateManager.DeleteTemplate(templateID)
	if err != nil {
		return fmt.Errorf("删除模板失败: %w", err)
	}

	fmt.Printf("模板 %s 删除成功\n", templateID)
	return nil
}

// exportTemplate 导出模板
func (a *App) exportTemplate(ctx context.Context, templateID, exportPath string) error {
	err := a.templateManager.ExportTemplate(templateID, exportPath)
	if err != nil {
		return fmt.Errorf("导出模板失败: %w", err)
	}

	fmt.Printf("模板 %s 导出成功: %s\n", templateID, exportPath)
	return nil
}

// importTemplate 导入模板
func (a *App) importTemplate(ctx context.Context, importPath string) error {
	tmpl, err := a.templateManager.ImportTemplate(importPath)
	if err != nil {
		return fmt.Errorf("导入模板失败: %w", err)
	}

	fmt.Printf("模板导入成功:\n")
	fmt.Printf("  新ID: %s\n", tmpl.ID)
	fmt.Printf("  名称: %s\n", tmpl.Name)
	fmt.Printf("  分类: %s\n", tmpl.Category)

	return nil
}
