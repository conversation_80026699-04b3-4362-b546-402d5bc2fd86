# Ollama CLI 架构文档

## 项目概述

Ollama CLI是一个轻量级的AI Agent实现，专门用于与Ollama API进行交互。项目采用Go语言开发，仅使用标准库，体现了简洁、高效的设计理念。

## 设计原则

1. **最小依赖**: 仅使用Go标准库，无外部依赖
2. **清晰分层**: 明确的包结构和职责分离
3. **错误处理**: 完善的错误处理和用户友好的错误消息
4. **可测试性**: 全面的单元测试覆盖
5. **可配置性**: 灵活的配置选项和命令行参数

## 架构组件

### 1. 主程序 (main.go)
- 程序入口点
- 命令行参数解析
- 配置加载和初始化
- CLI应用启动

### 2. CLI层 (internal/cli)
- **app.go**: CLI应用程序逻辑
- 命令路由和执行
- 用户交互处理
- 输出格式化

### 3. 配置管理 (internal/config)
- **config.go**: 配置结构和加载逻辑
- **config_test.go**: 配置功能测试
- 支持JSON配置文件
- 命令行参数覆盖机制

### 4. Ollama客户端 (internal/ollama)
- **client.go**: HTTP客户端实现
- **types.go**: API数据结构定义
- **client_test.go**: 客户端功能测试
- RESTful API封装
- 错误处理和重试机制

### 5. 错误处理 (internal/errors)
- **errors.go**: 自定义错误类型
- **errors_test.go**: 错误处理测试
- 错误分类和包装
- 用户友好的错误消息

### 6. 日志记录 (internal/logger)
- **logger.go**: 日志记录器
- 分级日志输出
- 详细模式支持

## 数据流

```
用户输入 → CLI解析 → 配置加载 → API客户端 → Ollama服务器
    ↓
错误处理 ← 响应处理 ← HTTP响应 ← JSON解析 ← API响应
    ↓
用户输出
```

## API支持

### 支持的Ollama API端点

1. **GET /api/tags** - 列出模型
2. **POST /api/generate** - 生成文本
3. **POST /api/chat** - 聊天对话
4. **POST /api/pull** - 下载模型
5. **POST /api/show** - 显示模型信息
6. **DELETE /api/delete** - 删除模型

### 请求/响应处理

- JSON序列化/反序列化
- HTTP状态码处理
- 流式响应支持（下载进度）
- 超时和重试机制

## 错误处理策略

### 错误分类

1. **NetworkError**: 网络连接问题
2. **APIError**: API调用错误
3. **ConfigError**: 配置相关错误
4. **ValidationError**: 参数验证错误
5. **UnknownError**: 未知错误

### 错误处理流程

```
原始错误 → 错误分类 → 错误包装 → 用户友好消息 → 输出
```

## 配置系统

### 配置优先级

1. 命令行参数（最高优先级）
2. 指定的配置文件
3. 默认配置文件 (~/.ollama-cli.json)
4. 默认值（最低优先级）

### 配置选项

- `host`: Ollama服务器地址
- `verbose`: 详细输出模式
- `timeout`: 请求超时时间

## 测试策略

### 单元测试覆盖

- 配置管理测试
- API客户端测试（使用mock服务器）
- 错误处理测试
- 边界条件测试

### 测试工具

- Go标准测试框架
- HTTP测试服务器
- 临时文件系统
- 覆盖率报告

## 构建和部署

### 构建选项

- 单平台构建: `go build`
- 多平台构建: `make build-all`
- 开发模式: `make dev`

### 支持平台

- Windows (amd64)
- Linux (amd64)
- macOS (amd64, arm64)

## 扩展性设计

### 添加新命令

1. 在`internal/cli/app.go`中添加命令处理
2. 在`internal/ollama/client.go`中添加API方法
3. 在`internal/ollama/types.go`中定义数据结构
4. 编写相应的单元测试

### 添加新配置选项

1. 在`internal/config/config.go`中添加字段
2. 更新默认配置
3. 添加命令行参数支持
4. 编写配置测试

## 性能考虑

### 内存使用

- 流式处理大响应
- 及时释放HTTP连接
- 避免不必要的数据复制

### 网络优化

- 连接复用
- 合理的超时设置
- 错误重试机制

## 安全考虑

### 输入验证

- 命令行参数验证
- 配置文件格式验证
- API参数验证

### 网络安全

- HTTPS支持
- 证书验证
- 安全的错误消息（不泄露敏感信息）

## 未来改进方向

1. **流式输出**: 支持实时显示生成内容
2. **插件系统**: 支持自定义命令扩展
3. **配置模板**: 预定义的配置模板
4. **批处理**: 支持批量操作
5. **性能监控**: 添加性能指标收集

## 总结

Ollama CLI项目成功实现了一个轻量级、功能完整的AI Agent客户端工具。通过清晰的架构设计、完善的错误处理和全面的测试覆盖，为用户提供了一个可靠、易用的Ollama API交互工具。项目的模块化设计也为未来的功能扩展奠定了良好的基础。
