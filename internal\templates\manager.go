package templates

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"text/template"
	"time"
)

// Template 提示词模板
type Template struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Category    string                 `json:"category"`
	Content     string                 `json:"content"`
	Variables   []TemplateVariable     `json:"variables"`
	Examples    []TemplateExample      `json:"examples"`
	Tags        []string               `json:"tags"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// TemplateVariable 模板变量
type TemplateVariable struct {
	Name        string `json:"name"`
	Type        string `json:"type"` // string, number, boolean, choice
	Description string `json:"description"`
	Required    bool   `json:"required"`
	Default     string `json:"default,omitempty"`
	Choices     []string `json:"choices,omitempty"` // for choice type
}

// TemplateExample 模板示例
type TemplateExample struct {
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Variables   map[string]string `json:"variables"`
	Expected    string            `json:"expected,omitempty"`
}

// Manager 模板管理器
type Manager struct {
	templatesDir string
}

// NewManager 创建模板管理器
func NewManager(templatesDir string) *Manager {
	return &Manager{
		templatesDir: templatesDir,
	}
}

// CreateTemplate 创建模板
func (m *Manager) CreateTemplate(name, description, category, content string, variables []TemplateVariable) (*Template, error) {
	if err := m.ensureTemplatesDir(); err != nil {
		return nil, err
	}

	template := &Template{
		ID:          generateTemplateID(name),
		Name:        name,
		Description: description,
		Category:    category,
		Content:     content,
		Variables:   variables,
		Examples:    make([]TemplateExample, 0),
		Tags:        make([]string, 0),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Metadata:    make(map[string]interface{}),
	}

	if err := m.SaveTemplate(template); err != nil {
		return nil, err
	}

	return template, nil
}

// SaveTemplate 保存模板
func (m *Manager) SaveTemplate(tmpl *Template) error {
	if err := m.ensureTemplatesDir(); err != nil {
		return err
	}

	tmpl.UpdatedAt = time.Now()
	
	filename := filepath.Join(m.templatesDir, tmpl.ID+".json")
	data, err := json.MarshalIndent(tmpl, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化模板失败: %w", err)
	}

	return os.WriteFile(filename, data, 0644)
}

// LoadTemplate 加载模板
func (m *Manager) LoadTemplate(templateID string) (*Template, error) {
	filename := filepath.Join(m.templatesDir, templateID+".json")
	
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取模板文件失败: %w", err)
	}

	var tmpl Template
	if err := json.Unmarshal(data, &tmpl); err != nil {
		return nil, fmt.Errorf("解析模板数据失败: %w", err)
	}

	return &tmpl, nil
}

// ListTemplates 列出所有模板
func (m *Manager) ListTemplates() ([]*Template, error) {
	if err := m.ensureTemplatesDir(); err != nil {
		return nil, err
	}

	files, err := os.ReadDir(m.templatesDir)
	if err != nil {
		return nil, fmt.Errorf("读取模板目录失败: %w", err)
	}

	var templates []*Template
	for _, file := range files {
		if !file.IsDir() && filepath.Ext(file.Name()) == ".json" {
			templateID := file.Name()[:len(file.Name())-5]
			tmpl, err := m.LoadTemplate(templateID)
			if err != nil {
				continue // 跳过损坏的模板文件
			}
			templates = append(templates, tmpl)
		}
	}

	return templates, nil
}

// DeleteTemplate 删除模板
func (m *Manager) DeleteTemplate(templateID string) error {
	filename := filepath.Join(m.templatesDir, templateID+".json")
	
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return fmt.Errorf("模板不存在: %s", templateID)
	}

	return os.Remove(filename)
}

// RenderTemplate 渲染模板
func (m *Manager) RenderTemplate(templateID string, variables map[string]string) (string, error) {
	tmpl, err := m.LoadTemplate(templateID)
	if err != nil {
		return "", err
	}

	// 验证必需变量
	for _, variable := range tmpl.Variables {
		if variable.Required {
			if _, exists := variables[variable.Name]; !exists {
				return "", fmt.Errorf("缺少必需变量: %s", variable.Name)
			}
		}
	}

	// 设置默认值
	for _, variable := range tmpl.Variables {
		if _, exists := variables[variable.Name]; !exists && variable.Default != "" {
			variables[variable.Name] = variable.Default
		}
	}

	// 渲染模板
	t, err := template.New("prompt").Parse(tmpl.Content)
	if err != nil {
		return "", fmt.Errorf("解析模板失败: %w", err)
	}

	var result strings.Builder
	if err := t.Execute(&result, variables); err != nil {
		return "", fmt.Errorf("渲染模板失败: %w", err)
	}

	return result.String(), nil
}

// GetTemplatesByCategory 根据分类获取模板
func (m *Manager) GetTemplatesByCategory(category string) ([]*Template, error) {
	allTemplates, err := m.ListTemplates()
	if err != nil {
		return nil, err
	}

	var templates []*Template
	for _, tmpl := range allTemplates {
		if tmpl.Category == category {
			templates = append(templates, tmpl)
		}
	}

	return templates, nil
}

// SearchTemplates 搜索模板
func (m *Manager) SearchTemplates(query string) ([]*Template, error) {
	allTemplates, err := m.ListTemplates()
	if err != nil {
		return nil, err
	}

	query = strings.ToLower(query)
	var templates []*Template

	for _, tmpl := range allTemplates {
		if strings.Contains(strings.ToLower(tmpl.Name), query) ||
		   strings.Contains(strings.ToLower(tmpl.Description), query) ||
		   strings.Contains(strings.ToLower(tmpl.Category), query) {
			templates = append(templates, tmpl)
		}

		// 搜索标签
		for _, tag := range tmpl.Tags {
			if strings.Contains(strings.ToLower(tag), query) {
				templates = append(templates, tmpl)
				break
			}
		}
	}

	return templates, nil
}

// AddExample 添加模板示例
func (m *Manager) AddExample(templateID string, example TemplateExample) error {
	tmpl, err := m.LoadTemplate(templateID)
	if err != nil {
		return err
	}

	tmpl.Examples = append(tmpl.Examples, example)
	return m.SaveTemplate(tmpl)
}

// UpdateTemplate 更新模板
func (m *Manager) UpdateTemplate(templateID string, updates map[string]interface{}) error {
	tmpl, err := m.LoadTemplate(templateID)
	if err != nil {
		return err
	}

	// 更新字段
	if name, ok := updates["name"].(string); ok {
		tmpl.Name = name
	}
	if description, ok := updates["description"].(string); ok {
		tmpl.Description = description
	}
	if category, ok := updates["category"].(string); ok {
		tmpl.Category = category
	}
	if content, ok := updates["content"].(string); ok {
		tmpl.Content = content
	}
	if tags, ok := updates["tags"].([]string); ok {
		tmpl.Tags = tags
	}

	return m.SaveTemplate(tmpl)
}

// ExportTemplate 导出模板
func (m *Manager) ExportTemplate(templateID, exportPath string) error {
	tmpl, err := m.LoadTemplate(templateID)
	if err != nil {
		return err
	}

	data, err := json.MarshalIndent(tmpl, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化模板失败: %w", err)
	}

	return os.WriteFile(exportPath, data, 0644)
}

// ImportTemplate 导入模板
func (m *Manager) ImportTemplate(importPath string) (*Template, error) {
	data, err := os.ReadFile(importPath)
	if err != nil {
		return nil, fmt.Errorf("读取导入文件失败: %w", err)
	}

	var tmpl Template
	if err := json.Unmarshal(data, &tmpl); err != nil {
		return nil, fmt.Errorf("解析导入数据失败: %w", err)
	}

	// 生成新的ID和时间戳
	tmpl.ID = generateTemplateID(tmpl.Name)
	tmpl.CreatedAt = time.Now()
	tmpl.UpdatedAt = time.Now()

	if err := m.SaveTemplate(&tmpl); err != nil {
		return nil, err
	}

	return &tmpl, nil
}

// InitializeBuiltinTemplates 初始化内置模板
func (m *Manager) InitializeBuiltinTemplates() error {
	builtinTemplates := getBuiltinTemplates()
	
	for _, tmpl := range builtinTemplates {
		// 检查模板是否已存在
		if _, err := m.LoadTemplate(tmpl.ID); err == nil {
			continue // 模板已存在，跳过
		}

		if err := m.SaveTemplate(tmpl); err != nil {
			return fmt.Errorf("保存内置模板失败: %w", err)
		}
	}

	return nil
}

// ensureTemplatesDir 确保模板目录存在
func (m *Manager) ensureTemplatesDir() error {
	return os.MkdirAll(m.templatesDir, 0755)
}

// generateTemplateID 生成模板ID
func generateTemplateID(name string) string {
	// 简化名称作为ID
	id := strings.ToLower(name)
	id = strings.ReplaceAll(id, " ", "_")
	id = strings.ReplaceAll(id, "-", "_")
	return fmt.Sprintf("tmpl_%s_%d", id, time.Now().Unix())
}

// getBuiltinTemplates 获取内置模板
func getBuiltinTemplates() []*Template {
	return []*Template{
		{
			ID:          "builtin_translate",
			Name:        "翻译助手",
			Description: "将文本翻译为指定语言",
			Category:    "翻译",
			Content:     "请将以下文本翻译为{{.target_language}}：\n\n{{.text}}",
			Variables: []TemplateVariable{
				{Name: "text", Type: "string", Description: "要翻译的文本", Required: true},
				{Name: "target_language", Type: "choice", Description: "目标语言", Required: true, 
				 Choices: []string{"英文", "中文", "日文", "韩文", "法文", "德文", "西班牙文"}},
			},
			Tags:      []string{"翻译", "语言", "国际化"},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:          "builtin_summarize",
			Name:        "内容总结",
			Description: "总结文本内容的要点",
			Category:    "总结",
			Content:     "请总结以下内容的{{.summary_type}}：\n\n{{.content}}\n\n要求：\n- 保持{{.length}}长度\n- 突出关键信息\n- 使用清晰的语言",
			Variables: []TemplateVariable{
				{Name: "content", Type: "string", Description: "要总结的内容", Required: true},
				{Name: "summary_type", Type: "choice", Description: "总结类型", Required: true, 
				 Choices: []string{"要点", "摘要", "核心观点", "关键信息"}, Default: "要点"},
				{Name: "length", Type: "choice", Description: "总结长度", Required: true, 
				 Choices: []string{"简短", "中等", "详细"}, Default: "中等"},
			},
			Tags:      []string{"总结", "摘要", "分析"},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:          "builtin_code_review",
			Name:        "代码审查",
			Description: "审查代码质量和提供改进建议",
			Category:    "编程",
			Content:     "请审查以下{{.language}}代码，并提供改进建议：\n\n```{{.language}}\n{{.code}}\n```\n\n请从以下方面进行评估：\n- 代码质量\n- 性能优化\n- 安全性\n- 可读性\n- 最佳实践",
			Variables: []TemplateVariable{
				{Name: "code", Type: "string", Description: "要审查的代码", Required: true},
				{Name: "language", Type: "choice", Description: "编程语言", Required: true, 
				 Choices: []string{"python", "javascript", "java", "go", "rust", "cpp", "csharp"}, Default: "python"},
			},
			Tags:      []string{"编程", "代码审查", "质量"},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:          "builtin_creative_writing",
			Name:        "创意写作",
			Description: "创作指定类型和风格的文本",
			Category:    "写作",
			Content:     "请创作一篇{{.type}}，主题是\"{{.topic}}\"。\n\n要求：\n- 风格：{{.style}}\n- 长度：{{.length}}\n- 目标读者：{{.audience}}\n\n{{if .additional_requirements}}额外要求：{{.additional_requirements}}{{end}}",
			Variables: []TemplateVariable{
				{Name: "type", Type: "choice", Description: "文本类型", Required: true, 
				 Choices: []string{"故事", "诗歌", "散文", "剧本", "演讲稿", "广告文案"}},
				{Name: "topic", Type: "string", Description: "主题", Required: true},
				{Name: "style", Type: "choice", Description: "写作风格", Required: true, 
				 Choices: []string{"正式", "轻松", "幽默", "严肃", "抒情", "现代"}, Default: "正式"},
				{Name: "length", Type: "choice", Description: "文本长度", Required: true, 
				 Choices: []string{"短篇", "中篇", "长篇"}, Default: "中篇"},
				{Name: "audience", Type: "string", Description: "目标读者", Required: false, Default: "一般读者"},
				{Name: "additional_requirements", Type: "string", Description: "额外要求", Required: false},
			},
			Tags:      []string{"写作", "创意", "文学"},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}
}
