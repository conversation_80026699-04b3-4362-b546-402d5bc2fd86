package errors

import (
	"fmt"
	"net"
	"net/url"
	"testing"
)

func TestAppError(t *testing.T) {
	originalErr := fmt.Errorf("原始错误")
	appErr := &AppError{
		Type:    NetworkError,
		Message: "网络错误",
		Cause:   originalErr,
	}
	
	expectedMsg := "网络错误: 原始错误"
	if appErr.Error() != expectedMsg {
		t.<PERSON><PERSON>("期望错误消息为 '%s', 实际为 '%s'", expectedMsg, appErr.Error())
	}
	
	if appErr.Unwrap() != originalErr {
		t.Errorf("期望Unwrap返回原始错误")
	}
}

func TestAppErrorWithoutCause(t *testing.T) {
	appErr := &AppError{
		Type:    ValidationError,
		Message: "验证错误",
	}
	
	if appErr.Error() != "验证错误" {
		t.Errorf("期望错误消息为 '验证错误', 实际为 '%s'", appErr.Error())
	}
	
	if appErr.Unwrap() != nil {
		t.Errorf("期望Unwrap返回nil")
	}
}

func TestNewNetworkError(t *testing.T) {
	cause := fmt.Errorf("连接被拒绝")
	err := NewNetworkError("网络连接失败", cause)
	
	if err.Type != NetworkError {
		t.Errorf("期望错误类型为NetworkError, 实际为 %v", err.Type)
	}
	
	if err.Message != "网络连接失败" {
		t.Errorf("期望错误消息为 '网络连接失败', 实际为 '%s'", err.Message)
	}
	
	if err.Cause != cause {
		t.Errorf("期望原因为指定的错误")
	}
}

func TestNewAPIError(t *testing.T) {
	cause := fmt.Errorf("HTTP 500")
	err := NewAPIError("API调用失败", cause)
	
	if err.Type != APIError {
		t.Errorf("期望错误类型为APIError, 实际为 %v", err.Type)
	}
}

func TestNewConfigError(t *testing.T) {
	cause := fmt.Errorf("配置文件不存在")
	err := NewConfigError("配置加载失败", cause)
	
	if err.Type != ConfigError {
		t.Errorf("期望错误类型为ConfigError, 实际为 %v", err.Type)
	}
}

func TestNewValidationError(t *testing.T) {
	err := NewValidationError("参数无效")
	
	if err.Type != ValidationError {
		t.Errorf("期望错误类型为ValidationError, 实际为 %v", err.Type)
	}
	
	if err.Cause != nil {
		t.Errorf("期望ValidationError没有原因")
	}
}

func TestIsNetworkError(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "nil error",
			err:      nil,
			expected: false,
		},
		{
			name:     "net.OpError",
			err:      &net.OpError{Op: "dial"},
			expected: true,
		},
		{
			name:     "url.Error",
			err:      &url.Error{Op: "Get"},
			expected: true,
		},
		{
			name:     "connection refused",
			err:      fmt.Errorf("connection refused"),
			expected: true,
		},
		{
			name:     "dial tcp error",
			err:      fmt.Errorf("dial tcp: connection timeout"),
			expected: true,
		},
		{
			name:     "regular error",
			err:      fmt.Errorf("regular error"),
			expected: false,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isNetworkError(tt.err)
			if result != tt.expected {
				t.Errorf("isNetworkError() = %v, 期望 %v", result, tt.expected)
			}
		})
	}
}

func TestIsAPIError(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "nil error",
			err:      nil,
			expected: false,
		},
		{
			name:     "HTTP error",
			err:      fmt.Errorf("HTTP 500 Internal Server Error"),
			expected: true,
		},
		{
			name:     "JSON error",
			err:      fmt.Errorf("json: cannot unmarshal"),
			expected: true,
		},
		{
			name:     "API error",
			err:      fmt.Errorf("API call failed"),
			expected: true,
		},
		{
			name:     "regular error",
			err:      fmt.Errorf("regular error"),
			expected: false,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isAPIError(tt.err)
			if result != tt.expected {
				t.Errorf("isAPIError() = %v, 期望 %v", result, tt.expected)
			}
		})
	}
}

func TestWrapError(t *testing.T) {
	tests := []struct {
		name         string
		err          error
		message      string
		expectedType ErrorType
	}{
		{
			name:         "nil error",
			err:          nil,
			message:      "test",
			expectedType: UnknownError, // 不会被调用，因为返回nil
		},
		{
			name:         "network error",
			err:          fmt.Errorf("connection refused"),
			message:      "网络错误",
			expectedType: NetworkError,
		},
		{
			name:         "API error",
			err:          fmt.Errorf("HTTP 404 Not Found"),
			message:      "API错误",
			expectedType: APIError,
		},
		{
			name:         "config error",
			err:          fmt.Errorf("invalid config format"),
			message:      "配置错误",
			expectedType: ConfigError,
		},
		{
			name:         "unknown error",
			err:          fmt.Errorf("unknown error"),
			message:      "未知错误",
			expectedType: UnknownError,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := WrapError(tt.err, tt.message)
			
			if tt.err == nil {
				if result != nil {
					t.Errorf("期望返回nil, 实际返回 %v", result)
				}
				return
			}
			
			if result == nil {
				t.Fatalf("期望返回AppError, 实际返回nil")
			}
			
			if result.Type != tt.expectedType {
				t.Errorf("期望错误类型为 %v, 实际为 %v", tt.expectedType, result.Type)
			}
			
			if result.Message != tt.message {
				t.Errorf("期望错误消息为 '%s', 实际为 '%s'", tt.message, result.Message)
			}
			
			if result.Cause != tt.err {
				t.Errorf("期望原因为指定的错误")
			}
		})
	}
}

func TestGetUserFriendlyMessage(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected string
	}{
		{
			name:     "nil error",
			err:      nil,
			expected: "",
		},
		{
			name:     "network error",
			err:      NewNetworkError("网络错误", nil),
			expected: "网络连接失败，请检查网络连接和Ollama服务器状态",
		},
		{
			name:     "API error",
			err:      NewAPIError("API错误", nil),
			expected: "API调用失败，请检查请求参数和服务器响应",
		},
		{
			name:     "config error",
			err:      NewConfigError("配置错误", nil),
			expected: "配置错误，请检查配置文件和参数设置",
		},
		{
			name:     "validation error",
			err:      NewValidationError("验证错误"),
			expected: "参数验证失败，请检查输入参数",
		},
		{
			name:     "unknown app error",
			err:      &AppError{Type: UnknownError, Message: "未知错误"},
			expected: "发生未知错误",
		},
		{
			name:     "regular error",
			err:      fmt.Errorf("regular error"),
			expected: "regular error",
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetUserFriendlyMessage(tt.err)
			if result != tt.expected {
				t.Errorf("期望用户友好消息为 '%s', 实际为 '%s'", tt.expected, result)
			}
		})
	}
}
